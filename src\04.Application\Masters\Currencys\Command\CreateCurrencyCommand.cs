﻿using MediatR;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Masters;
using Pertamina.WebFIPosting.Shared.Masters.Currency.Command;

namespace Pertamina.WebFIPosting.Application.Masters.Currencys.Command;

public class CreateCurrencyCommand : CurrencyRequest, IRequest<CurrencyResponse>
{
}

public class CreateCurrencyCommandHandler : IRequestHandler<CreateCurrencyCommand, CurrencyResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    public CreateCurrencyCommandHandler(IWebFiPlatformDbContext context)
    {
        _context = context;
    }

    public async Task<CurrencyResponse> Handle(CreateCurrencyCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var setData = new Currency
            {
                CurrencyCode = request.CurrencyCode,
                LongText = request.LongText,
                ShortText = request.ShortText,
                ISOCode = request.ISOCode,
                AlternativeKey = request.AlternativeKey,
                ValidUntil = request.ValidUntil,
                PrimarySAPCode = request.PrimarySAPCode
            };

            await _context.Currency.AddAsync(setData, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            return new CurrencyResponse() { CurrencyCode = setData.CurrencyCode };
        }
        catch (Exception ex)
        {
            throw new NotImplementedException("Error while creating Currency", ex);
        }
    }
}
