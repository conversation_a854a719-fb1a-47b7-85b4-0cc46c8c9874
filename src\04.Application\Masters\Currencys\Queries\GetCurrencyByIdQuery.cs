﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Masters;
using Pertamina.WebFIPosting.Shared.Masters.Currency.Constants;
using Pertamina.WebFIPosting.Shared.Masters.Currency.Queries;

namespace Pertamina.WebFIPosting.Application.Masters.Currencys.Queries;
public class GetCurrencyByIdQuery : IRequest<GetCurrencyResponse>
{
    public string CurrencyCode { get; set; } = default!;
}

public class GetCurrencyByIdQueryMapper : IMapFrom<Currency, GetCurrencyResponse>
{
}

public class GetCurrencyByIdQueryHandler : IRequestHandler<GetCurrencyByIdQuery, GetCurrencyResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;
    public GetCurrencyByIdQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }
    public async Task<GetCurrencyResponse> Handle(GetCurrencyByIdQuery request, CancellationToken cancellationToken)
    {
        var query = await _context.Currency
           .Where(x => x.CurrencyCode == request.CurrencyCode).FirstOrDefaultAsync(cancellationToken);

        if (query is null)
        {
            throw new NotFoundException(DisplayTextFor.Currency, request.CurrencyCode);
        }

        return _mapper.Map<GetCurrencyResponse>(query);
    }
}
