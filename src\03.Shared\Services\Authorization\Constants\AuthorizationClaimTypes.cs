﻿/*
* InvestorRelationsHub edited by arie.sem
* Copyright (c) 2024-2025 PT. Pertamina Persero
*/

namespace Pertamina.WebFIPosting.Shared.Services.Authorization.Constants;

public static class AuthorizationClaimTypes
{
    public const string PositionName = "position_name";
    public const string PositionId = "position_id";
    public const string Permission = "permission";
    public const string CustomParameter = "custom_parameter";
    public const string Role = "roles";
    public const string NameIdentifier = "nameidentifier";
    public const string PreferredUsername = "preferred_username";
    public const string EmployeeNumber = "employee_number";
}
