﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Extensions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Masters;
using Pertamina.WebFIPosting.Shared.Common.Responses;
using Pertamina.WebFIPosting.Shared.Masters.MasterCompanyCodeCurency.Queries;

namespace Pertamina.WebFIPosting.Application.Masters.Queries;
public class GetMasterCompanyCodeCurencyQuery : IRequest<ListResponse<GetMasterCompanyCodeCurencyQueryResponse>>
{
}

public class GetMasterCompanyCodeCurencyQueryMapping : IMapFrom<MasterCompanyCodeCurency, GetMasterCompanyCodeCurencyQueryResponse>
{
    public void Mapping(Profile profile)
    {
        if (profile is not null)
        {
            profile.CreateMap<MasterCompanyCodeCurency, GetMasterCompanyCodeCurencyQueryResponse>();
        }
    }
}

public class GetMasterCompanyCodeCurencyQueryHandler : IRequestHandler<GetMasterCompanyCodeCurencyQuery, ListResponse<GetMasterCompanyCodeCurencyQueryResponse>>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;

    public GetMasterCompanyCodeCurencyQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<ListResponse<GetMasterCompanyCodeCurencyQueryResponse>> Handle(GetMasterCompanyCodeCurencyQuery request, CancellationToken cancellationToken)
    {
        var query = await _context.MasterCompanyCodeCurency
            .AsNoTracking()
            //.Where(x => !x.IsDeleted)
            .ProjectTo<GetMasterCompanyCodeCurencyQueryResponse>(_mapper.ConfigurationProvider)
            .ToListAsync(cancellationToken);

        return query.ToListResponse();
    }
}
