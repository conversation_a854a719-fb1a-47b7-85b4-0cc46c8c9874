﻿using MediatR;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Application.Services.Storage;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgerFiles.Queries;

namespace Pertamina.WebFIPosting.Application.Transactions.GeneralLedgerFiles.Commands;
public class DeleteGeneralLedgerFileCommand : IRequest<GetGeneralLedgerFileResponse>
{
    public Guid Id { get; set; }
}
public class DeleteGeneralLedgerFileCommandHandler : IRequestHandler<DeleteGeneralLedgerFileCommand, GetGeneralLedgerFileResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IStorageService _storageService;
    public DeleteGeneralLedgerFileCommandHandler(IWebFiPlatformDbContext context, IStorageService storageService)
    {
        _context = context;
        _storageService = storageService;
    }

    public async Task<GetGeneralLedgerFileResponse> Handle(DeleteGeneralLedgerFileCommand request, CancellationToken cancellationToken)
    {
        var generalLedgerFile = await _context.GeneralLedgerFile.FindAsync(request.Id);
        if (generalLedgerFile == null)
        {
            throw new NotFoundException("General Ledger File not found.");
        }

        if (!string.IsNullOrEmpty(generalLedgerFile.StorageFileId))
        {
            await _storageService.DeleteAsync(generalLedgerFile.StorageFileId);
        }

        _context.GeneralLedgerFile.Remove(generalLedgerFile);
        await _context.SaveChangesAsync(cancellationToken);

        return new GetGeneralLedgerFileResponse();
    }
}
