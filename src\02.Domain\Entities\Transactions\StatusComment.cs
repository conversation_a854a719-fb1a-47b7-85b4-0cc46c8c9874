﻿using Pertamina.WebFIPosting.Base.Enums;
using Pertamina.WebFIPosting.Domain.Abstracts;

namespace Pertamina.WebFIPosting.Domain.Entities.Transactions;

public class StatusComment : AuditableEntity
{
    public Guid? StatusHistoryId { get; set; }
    public StatusHistory? StatusHistory { get; set; } = default!;
    public RequestStatus? Status { get; set; } = default!;
    public string? Comment { get; set; } = default!;
}
