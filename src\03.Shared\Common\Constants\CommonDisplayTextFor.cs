﻿using Pertamina.WebFIPosting.Shared.Common.Extensions;

namespace Pertamina.WebFIPosting.Shared.Common.Constants;

public static class CommonDisplayTextFor
{
    public const string Home = nameof(Home);
    public const string About = nameof(About);
    public const string Index = nameof(Index);
    public const string Dashboard = nameof(Dashboard);
    public static readonly string RecordtoReport = "Record to Report";
    public static readonly string GLPosting = nameof(GLPosting).SplitWords();
    public static readonly string ResetDocumentClearing = nameof(ResetDocumentClearing).SplitWords();
    public static readonly string Reverse = nameof(Reverse);
    public static readonly string Recurring = nameof(Recurring);
    public static readonly string PostingClearing = "GL Posting With Clearing";
    public static readonly string ManualBankStatement = nameof(ManualBankStatement).SplitWords();

    public static readonly string HydrocarbonCostAccountingSupport = nameof(HydrocarbonCostAccountingSupport).SplitWords();
    public static readonly string ManualReposting = nameof(ManualReposting).SplitWords();
    public static readonly string SKFCalculation = nameof(SKFCalculation).SplitWords();
    public static readonly string ManualAllocation = nameof(ManualAllocation).SplitWords();

    public static readonly string OrdertoCash = "Order to Cash";
    public static readonly string BillingFI = "Manual Billing & Unbilled Customer";
    public static readonly string ManualBillingNonICT = "Manual Billing & Unbilled Customer Non ICT (F-04)";
    public static readonly string ManualBillingICT = "Manual Billing & Unbilled Customer ICT (F-04)";
    public static readonly string ManualBillingSD = nameof(ManualBillingSD).SplitWords();

    public static readonly string Reporting = nameof(Reporting).SplitWords();
    public static readonly string RecordToReport = nameof(RecordToReport).SplitWords();
    public static readonly string HydrocarbonCAS = "Hydrocarbon Cost Accounting Support";
    public static readonly string FixedAssetAccounting = nameof(FixedAssetAccounting).SplitWords();
    public static readonly string OrdertoCashReport = "Order to Cash";

    public static readonly string ReportGLPosting = "GL Posting";

    public static readonly string SiteAdministrator = nameof(SiteAdministrator).SplitWords();
    public static readonly string MasterCompanyCodeCurrency = nameof(MasterCompanyCodeCurrency).SplitWords();
    public static readonly string MasterCompanyCodeClient = nameof(MasterCompanyCodeClient).SplitWords();
    public static readonly string Currency = nameof(Currency);

    public const string Id = "ID";
    public const string On = nameof(On);

    public const string Action = nameof(Action);
    public const string Required = nameof(Required);

    public const string Submit = nameof(Submit);
    public const string Submitted = nameof(Submitted);

    public const string Create = nameof(Create);
    public const string Created = nameof(Created);

    public const string Add = nameof(Add);
    public const string Added = nameof(Added);

    public const string Commit = nameof(Commit);
    public const string Committed = nameof(Committed);

    public const string Edit = nameof(Edit);
    public const string Edited = nameof(Edited);

    public const string Modify = nameof(Modify);
    public const string Modified = nameof(Modified);

    public const string Update = nameof(Update);
    public const string Updated = nameof(Updated);

    public const string Refresh = nameof(Refresh);
    public const string Refreshed = nameof(Refreshed);

    public const string Reload = nameof(Reload);
    public const string Reloaded = nameof(Reloaded);

    public const string Enable = nameof(Enable);
    public const string Enabled = nameof(Enabled);

    public const string Disable = nameof(Disable);
    public const string Disabled = nameof(Disabled);

    public const string Delete = nameof(Delete);
    public const string Deleted = nameof(Deleted);

    public const string Continue = nameof(Continue);
    public const string Continued = nameof(Continued);

    public const string Select = nameof(Select);
    public const string Selected = nameof(Selected);

    public const string Download = nameof(Download);
    public const string Downloaded = nameof(Downloaded);

    public const string Upload = nameof(Upload);
    public const string Uploaded = nameof(Uploaded);

    public const string Import = nameof(Import);
    public const string Imported = nameof(Imported);

    public const string Export = nameof(Export);
    public const string Exported = nameof(Exported);

    public const string View = nameof(View);
    public const string Search = nameof(Search);
    public const string Filter = nameof(Filter);
    public const string Apply = nameof(Apply);
    public const string Confirm = nameof(Confirm);
    public const string Save = nameof(Save);
    public const string Close = nameof(Close);
    public const string Saved = nameof(Saved);
    public const string Cancel = nameof(Cancel);
    public const string Dismiss = nameof(Dismiss);
    public const string Get = nameof(Get);

    public const string By = nameof(By);
    public static readonly string CreatedBy = nameof(CreatedBy).SplitWords();
    public static readonly string ModifiedBy = nameof(ModifiedBy).SplitWords();

    public const string File = nameof(File);
    public static readonly string FileName = nameof(FileName).SplitWords();
    public static readonly string FileSize = nameof(FileSize).SplitWords();

    public static readonly string GeneralInfo = nameof(GeneralInfo).SplitWords();
    public const string Tables = nameof(Tables);
    public const string Charts = nameof(Charts);

    public const string Unsupported = nameof(Unsupported);
    public const string Service = nameof(Service);
    public const string Error = nameof(Error);
}
