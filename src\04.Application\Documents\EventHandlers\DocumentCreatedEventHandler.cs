﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.DomainEvent.Models;
using Pertamina.WebFIPosting.Application.Services.Ecm;
using Pertamina.WebFIPosting.Application.Services.Ecm.Models.UploadContent;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Application.Services.Storage;
using Pertamina.WebFIPosting.Domain.Events;
using Pertamina.WebFIPosting.Shared.Documents.Constants;

namespace Pertamina.WebFIPosting.Application.Documents.EventHandlers;

public class DocumentCreatedEventHandler : INotificationHandler<DomainEventNotification<DocumentCreatedEvent>>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IStorageService _storageService;
    private readonly IEcmService _ecmService;

    public DocumentCreatedEventHandler(
        IWebFiPlatformDbContext context,
        IStorageService storageService,
        IEcmService ecmService)
    {
        _context = context;
        _storageService = storageService;
        _ecmService = ecmService;
    }

    public async Task Handle(DomainEventNotification<DocumentCreatedEvent> notification, CancellationToken cancellationToken)
    {
        var document = await _context.Documents
            .Where(x => !x.IsDeleted && x.Id == notification.DomainEvent.DocumentId)
            .SingleOrDefaultAsync(cancellationToken);

        if (document is null)
        {
            throw new NotFoundException(DisplayTextFor.Document, notification.DomainEvent.DocumentId);
        }

        var uploadContentModel = new UploadContentRequest
        {
            CompanyCode = document.CompanyCode,
            JrdpYear = document.JrdpYear,
            TopicCode = document.TopicCode,
            DocumentCategoryName = document.DocumentCategoryName,
            FileName = document.FileName,
            FileContentType = document.FileContentType,
            FileContent = await _storageService.ReadAsync(document.StorageFileId)
        };

        var uploadContentResult = await _ecmService.UploadContentAsync(uploadContentModel, cancellationToken);

        if (uploadContentResult.ContentId.HasValue)
        {
            document.EcmContentId = uploadContentResult.ContentId.Value.ToString();
            await _context.SaveChangesAsync(this, cancellationToken);
        }
    }
}
