﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Pertamina.WebFIPosting.Application.Common.Attributes;
using Pertamina.WebFIPosting.Application.Common.Extensions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities;
using Pertamina.WebFIPosting.Shared.Audits.Options;
using Pertamina.WebFIPosting.Shared.Audits.Queries.GetAudits;
using Pertamina.WebFIPosting.Shared.Common.Enums;
using Pertamina.WebFIPosting.Shared.Common.Responses;
using Pertamina.WebFIPosting.Shared.Services.Authorization.Constants;

namespace Pertamina.WebFIPosting.Application.Audits.Queries.GetAudits;

[Authorize(Policy = Permissions.WebFi_Api_Audience)]
public class GetAuditsQuery : GetAuditsRequest, IRequest<PaginatedListResponse<GetAuditsAudit>>
{
}

public class GetAuditsQueryValidator : AbstractValidator<GetAuditsQuery>
{
    public GetAuditsQueryValidator(IOptions<AuditOptions> auditOptions)
    {
        Include(new GetAuditsRequestValidator(auditOptions));
    }
}

public class GetAuditsAuditMapping : IMapFrom<Audit, GetAuditsAudit>
{
}

public class GetAuditsQueryHandler : IRequestHandler<GetAuditsQuery, PaginatedListResponse<GetAuditsAudit>>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly AuditOptions _auditOptions;
    private readonly IMapper _mapper;

    public GetAuditsQueryHandler(IWebFiPlatformDbContext context, IOptions<AuditOptions> auditOptions, IMapper mapper)
    {
        _context = context;
        _auditOptions = auditOptions.Value;
        _mapper = mapper;
    }

    public async Task<PaginatedListResponse<GetAuditsAudit>> Handle(GetAuditsQuery request, CancellationToken cancellationToken)
    {
        var from = request.From ?? _auditOptions.FilterMinimumCreated;
        var to = request.To ?? _auditOptions.FilterMaximumCreated;

        var query = _context.Audits
            .AsNoTracking()
            .Where(x => x.Created >= from && x.Created <= to)
            .ApplySearch(request.SearchText, typeof(GetAuditsAudit), _mapper.ConfigurationProvider)
            .ApplyOrder(request.SortField, request.SortOrder,
                typeof(GetAuditsAudit),
                _mapper.ConfigurationProvider,
                nameof(GetAuditsAudit.Created),
                SortOrder.Desc);

        var result = await query
            .ProjectTo<GetAuditsAudit>(_mapper.ConfigurationProvider)
            .ToPaginatedListAsync(request.Page, request.PageSize, cancellationToken);

        return result.ToPaginatedListResponse();
    }
}
