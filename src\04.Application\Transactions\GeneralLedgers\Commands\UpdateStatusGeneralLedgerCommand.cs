﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Bmc;
using Pertamina.WebFIPosting.Application.Services.CurrentUser;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Transactions;
using Pertamina.WebFIPosting.Shared.Bmcs.Command;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgers.Commands;

namespace Pertamina.WebFIPosting.Application.Transactions.GeneralLedgers.Commands;

public class UpdateStatusGeneralLedgerCommand : GeneralLedgerStatusRequest, IRequest<GeneralLedgerResponse>
{
}

public class UpdateStatusGeneralLedgerCommandHandler : IRequestHandler<UpdateStatusGeneralLedgerCommand, GeneralLedgerResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IBmcService _bmcService;
    private readonly ICurrentUserService _user;
    public UpdateStatusGeneralLedgerCommandHandler(IWebFiPlatformDbContext context, IBmcService bmcService, ICurrentUserService userService)
    {
        _context = context;
        _bmcService = bmcService;
        _user = userService;
    }
    public async Task<GeneralLedgerResponse> Handle(UpdateStatusGeneralLedgerCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var validStatus = false;
            var query = await _context.GeneralLedger.FindAsync(request.Id, cancellationToken);
            if (query == null)
            {
                throw new NotFoundException("General Ledger not found.");
            }

            if (request.Status == Base.Enums.RequestStatus.Approved)
            {
                var queryStatus = await _context.StatusHistory
                 .Where(x => x.GeneralLedgerId == request.Id && x.StatusType == "RR")
                 .Where(x => x.Status == query.Status)
                 .FirstOrDefaultAsync(cancellationToken);

                if (queryStatus.TotalApproval - 1 > 0)
                {
                    queryStatus.TotalApproval = queryStatus.TotalApproval - 1;
                    var historyStatusComment = new StatusComment()
                    {
                        StatusHistoryId = queryStatus.Id,
                        Status = Base.Enums.RequestStatus.Approved,
                        Comment = request.Comment
                    };

                    await _context.StatusComment.AddAsync(historyStatusComment, cancellationToken);
                }
                else
                {
                    queryStatus.TotalApproval = queryStatus.TotalApproval - 1;
                    var historyStatusComment = new StatusComment()
                    {
                        StatusHistoryId = queryStatus.Id,
                        Status = Base.Enums.RequestStatus.Approved,
                        Comment = request.Comment
                    };

                    await _context.StatusComment.AddAsync(historyStatusComment, cancellationToken);

                    var historyStatus = new StatusHistory()
                    {
                        Status = Base.Enums.RequestStatus.Approved,
                        StatusType = "RR",
                        GeneralLedgerId = query.Id,
                        TotalApproval = 0,
                        Notes = request.Comment
                    };

                    await _context.StatusHistory.AddAsync(historyStatus, cancellationToken);
                    query.Status = Base.Enums.RequestStatus.Approved;

                    #region Service BMC For WO
                    var responseBmc = await _bmcService.CreateTicketAsync(new New_Create_Operation
                    {
                        Kode_Layanan = "Web_FI_Posting_001",
                        Notes = $"Request GLP No {query.TicketNo} telah disetujui oleh {_user.Username}",
                        Requester_LoginID = _user.Username,
                        Transaction_Number = query.TicketNo,
                    }, _context);
                    #endregion
                    query.WorkOrderNo = responseBmc.WorkOrderNo;
                }
            }
            else if (request.Status == Base.Enums.RequestStatus.Rejected)
            {
                query.Status = Base.Enums.RequestStatus.Rejected;
                var historyStatus = new StatusHistory()
                {
                    Status = (Base.Enums.RequestStatus)request.Status,
                    StatusType = "RR",
                    GeneralLedgerId = query.Id,
                    TotalApproval = 0,
                    Notes = request.Comment
                };

                await _context.StatusHistory.AddAsync(historyStatus, cancellationToken);

                var historyStatusComment = new StatusComment()
                {
                    StatusHistoryId = historyStatus.Id,
                    Comment = request.Comment
                };

                await _context.StatusComment.AddAsync(historyStatusComment, cancellationToken);
            }
            else if (request.Status == Base.Enums.RequestStatus.Assigned)
            {
                var i = 0;
                while (i < 2)
                {
                    var historyStatus = new StatusHistory()
                    {
                        Status = i == 0 ? Base.Enums.RequestStatus.Assigned : Base.Enums.RequestStatus.InProgress,
                        StatusType = "RR",
                        GeneralLedgerId = query.Id,
                        TotalApproval = 0,
                        Notes = request.Comment
                    };

                    await _context.StatusHistory.AddAsync(historyStatus, cancellationToken);

                    var historyStatusComment = new StatusComment()
                    {
                        StatusHistoryId = historyStatus.Id,
                        Status = i != 0 ? Base.Enums.RequestStatus.SAPProcess : null,
                        Comment = request.Comment
                    };
                    await _context.StatusComment.AddAsync(historyStatusComment, cancellationToken);
                    i++;
                }

                query.Status = Base.Enums.RequestStatus.InProgress;
                query.StatusSap = Base.Enums.RequestStatus.SAPProcess;
                query.SupportGroup = request.SupportGroup;
                query.AssignTo = request.AssignTo;
                query.NameAssignTo = request.NameAssignTo;
                query.EmailAssignTo = request.EmailAssignTo;
                query.ProffofExecution = request.ProffofExecution;
            }
            else if (request.Status == Base.Enums.RequestStatus.Posted)
            {
                var queryStatusHistory = await _context.StatusHistory
                    .Where(x => x.GeneralLedgerId == request.Id && x.StatusType == "RR")
                    .Where(x => x.Status == query.Status)
                    .FirstOrDefaultAsync(cancellationToken);

                var historyStatusComment = new StatusComment()
                {
                    StatusHistoryId = queryStatusHistory!.Id,
                    Status = Base.Enums.RequestStatus.Posted,
                    Comment = "Update Status SAP",
                };
                query.StatusSap = Base.Enums.RequestStatus.Posted;

                await _context.StatusComment.AddAsync(historyStatusComment, cancellationToken);
            }
            else if (request.Status == Base.Enums.RequestStatus.Completed)
            {
                query.Status = Base.Enums.RequestStatus.Completed;
                var historyStatus = new StatusHistory()
                {
                    Status = (Base.Enums.RequestStatus)request.Status,
                    StatusType = "RR",
                    GeneralLedgerId = query.Id,
                    TotalApproval = 0,
                    Notes = request.Comment
                };

                await _context.StatusHistory.AddAsync(historyStatus, cancellationToken);

                var historyStatusComment = new StatusComment()
                {
                    StatusHistoryId = historyStatus.Id,
                    Comment = request.Comment
                };

                await _context.StatusComment.AddAsync(historyStatusComment, cancellationToken);
            }

            else if (request.Status == Base.Enums.RequestStatus.InProgress)
            {
                query.Status = Base.Enums.RequestStatus.InProgress;
                var historyStatus = new StatusHistory()
                {
                    Status = (Base.Enums.RequestStatus)request.Status,
                    StatusType = "RR",
                    GeneralLedgerId = query.Id,
                    TotalApproval = query.Approver1 != null && query.Approver2 != null ? 2 : query.Approver1 != null && query.Approver2 != null && query.Approver3 != null ? 3 : 1,
                    Notes = request.Comment
                };

                await _context.StatusHistory.AddAsync(historyStatus, cancellationToken);

                var historyStatusComment = new StatusComment()
                {
                    StatusHistoryId = historyStatus.Id,
                    Comment = request.Comment
                };
                await _context.StatusComment.AddAsync(historyStatusComment, cancellationToken);
            }

            await _context.SaveChangesAsync(this, cancellationToken);

            return new GeneralLedgerResponse { Id = query.Id, ValidStatus = validStatus };

        }
        catch (Exception ex)
        {
            throw new NotImplementedException($"{ex.Message}");
        }
    }
}
