﻿namespace Pertamina.WebFIPosting.Domain.Entities.Masters;

public class MasterValidationApprovalRR
{
    public string SubLayanan { get; set; } = default!;
    public string CompanyCode { get; set; } = default!;
    public string? TipeData { get; set; } = default!;
    public string? Requestor { get; set; } = default!;
    public string? VarianRequestor { get; set; } = default!;
    public string? Currency { get; set; } = default!;
    public decimal? NilaiAwal { get; set; } = default!;
    public decimal? NilaiAkhir { get; set; } = default!;
    public string? KonversiMataUangAsing { get; set; } = default!;
    public string? FlagEA { get; set; } = default!;
    public string? KonversiUSDkeIDR { get; set; } = default!;
    public string? IDPos1 { get; set; } = default!;
    public string? Layer1 { get; set; } = default!;
    public string? Approver1 { get; set; } = default!;
    public string? ApproverName1 { get; set; } = default!;
    public string? ApproverLoginID1 { get; set; } = default!;
    public string? IDPos2 { get; set; } = default!;
    public string? Layer2 { get; set; } = default!;
    public string? Approver2 { get; set; } = default!;
    public string? ApproverName2 { get; set; } = default!;
    public string? ApproverLoginID2 { get; set; } = default!;
    public string? IDPos3 { get; set; } = default!;
    public string? Layer3 { get; set; } = default!;
    public string? Approver3 { get; set; } = default!;
    public string? ApproverName3 { get; set; } = default!;
    public string? ApproverLoginID3 { get; set; } = default!;
    public string? IDPos4 { get; set; } = default!;
    public string? Layer4 { get; set; } = default!;
    public string? Approver4 { get; set; } = default!;
    public string? ApproverName4 { get; set; } = default!;
    public string? ApproverLoginID4 { get; set; } = default!;
    public string? IDPos5 { get; set; } = default!;
    public string? Layer5 { get; set; } = default!;
    public string? Approver5 { get; set; } = default!;
    public string? ApproverName5 { get; set; } = default!;
    public string? ApproverLoginID5 { get; set; } = default!;
}
