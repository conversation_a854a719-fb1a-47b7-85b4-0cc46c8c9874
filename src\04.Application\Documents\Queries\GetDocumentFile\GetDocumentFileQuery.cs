﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Attributes;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Application.Services.Storage;
using Pertamina.WebFIPosting.Shared.Documents.Constants;
using Pertamina.WebFIPosting.Shared.Documents.Queries.GetDocumentFile;
using Pertamina.WebFIPosting.Shared.Services.Authorization.Constants;

namespace Pertamina.WebFIPosting.Application.Documents.Queries.GetDocumentFile;

[Authorize(Policy = Permissions.WebFi_Api_Audience)]
public class GetDocumentFileQuery : IRequest<GetDocumentFileResponse>
{
    public Guid DocumentId { get; set; }
}

public class GetDocumentFileHandler : IRequestHandler<GetDocumentFileQuery, GetDocumentFileResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IStorageService _storageService;

    public GetDocumentFileHandler(IWebFiPlatformDbContext context, IStorageService storageService)
    {
        _context = context;
        _storageService = storageService;
    }

    public async Task<GetDocumentFileResponse> Handle(GetDocumentFileQuery request, CancellationToken cancellationToken)
    {
        var ticketAttachment = await _context.Documents
            .Where(x => !x.IsDeleted && x.Id == request.DocumentId)
            .SingleOrDefaultAsync(cancellationToken);

        if (ticketAttachment is null)
        {
            throw new NotFoundException(DisplayTextFor.Document, request.DocumentId);
        }

        var response = new GetDocumentFileResponse
        {
            FileName = $"{ticketAttachment.FileName}",
            ContentType = ticketAttachment.FileContentType,
            Content = await _storageService.ReadAsync(ticketAttachment.StorageFileId)
        };

        return response;
    }
}
