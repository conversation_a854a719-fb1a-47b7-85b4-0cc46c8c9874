﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Shared.Transactions.GLPostingClearingLineDetails.Commands;

namespace Pertamina.WebFIPosting.Application.Transactions.GLPostingClearingLineDetails.Commands;
public class UpdateGLPostingClearingLineDetailsCommand : GLPostingClearingLineDetailRequest, IRequest<GLPostingClearingLineDetailResponse>
{
}
public class UpdateGLPostingClearingLineDetailsCommandValidator : AbstractValidator<UpdateGLPostingClearingLineDetailsCommand>
{
    public UpdateGLPostingClearingLineDetailsCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("ID is required.");

        RuleFor(x => x.GeneralLedgerId)
            .NotEmpty().WithMessage("General Ledger ID is required.");
    }
}
public class UpdateGLPostingClearingLineDetailsCommandHandler : IRequestHandler<UpdateGLPostingClearingLineDetailsCommand, GLPostingClearingLineDetailResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    public UpdateGLPostingClearingLineDetailsCommandHandler(IWebFiPlatformDbContext context)
    {
        _context = context;
    }

    public async Task<GLPostingClearingLineDetailResponse> Handle(UpdateGLPostingClearingLineDetailsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var existingData = await _context.GLPostingClearingLineDetail
               .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

            if (existingData == null)
            {
                throw new NotFoundException($"GLPosting Clearing Line Detail with ID {request.Id} not found.");
            }

            var generalLedgerExists = await _context.GeneralLedger
                .AnyAsync(x => x.Id == request.GeneralLedgerId, cancellationToken);
            if (!generalLedgerExists)
            {
                throw new NotFoundException($"General Ledger with ID {request.GeneralLedgerId} not found.");
            }

            existingData.GeneralLedgerId = request.GeneralLedgerId;
            existingData.Item = request.Item;
            existingData.CompanyCode = request.CompanyCode;
            existingData.DocumentDate = request.DocumentDate;
            existingData.PostingDate = request.PostingDate;
            existingData.Period = request.Period;
            existingData.TranslationDate = request.TranslationDate;
            existingData.DebitCredit = request.DebitCredit;
            existingData.AccountType = request.AccountType;
            existingData.GLAccount = request.GLAccount;
            existingData.Reference = request.Reference;
            existingData.DocHeaderText = request.DocHeaderText;
            existingData.DocType = request.DocType;
            existingData.Currency = request.Currency;
            existingData.AmountinDoc = request.AmountinDoc;
            existingData.AmountinLC = request.AmountinLC;
            existingData.AmountinLC2 = request.AmountinLC2;
            existingData.AmountinLC3 = request.AmountinLC3;
            existingData.Text = request.Text;
            existingData.TaxCode = request.TaxCode;
            existingData.Assignment = request.Assignment;
            existingData.CostCenter = request.CostCenter;
            existingData.Plant = request.Plant;
            existingData.ProfitCenter = request.ProfitCenter;
            existingData.InternalOrder = request.InternalOrder;
            existingData.WBS = request.WBS;
            existingData.BussinessPlace = request.BussinessPlace;
            existingData.Rec_Indicator = request.Rec_Indicator;
            existingData.Reference_Key_1 = request.Reference_Key_1;
            existingData.Reference_Key_2 = request.Reference_Key_2;
            existingData.Reference_Key_3 = request.Reference_Key_3;
            existingData.Trading_Partner = request.Trading_Partner;
            existingData.TransactnType = request.TransactnType;
            existingData.NoDocumentClearing = request.NoDocumentClearing;
            existingData.YearDocumentClearing = request.YearDocumentClearing;
            existingData.AmountDocumentClearing = request.AmountDocumentClearing;
            existingData.DocNumberPosted = request.DocNumberPosted;
            existingData.DocCurrency = request.DocCurrency;
            existingData.Year = request.Year;
            existingData.PostedAmmount = request.PostedAmmount;
            existingData.Message = request.Message;
            existingData.Status = request.Status;
            existingData.SimulatedResponseCode = request.SimulatedResponseCode;
            existingData.SimulatedResponseMessage = request.SimulatedResponseMessage;
            existingData.IsSimulate = request.IsSimulate;
            existingData.SimulateDate = request.SimulateDate;

            await _context.SaveChangesAsync(cancellationToken);

            return new GLPostingClearingLineDetailResponse { Id = existingData.Id };
        }
        catch (Exception ex)
        {
            throw new NotImplementedException("Error while updating GLPosting Clearing Line Detail", ex);
        }
    }
}
