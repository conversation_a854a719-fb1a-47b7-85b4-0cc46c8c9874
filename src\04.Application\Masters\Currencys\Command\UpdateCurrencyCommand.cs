﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Shared.Masters.Currency.Command;

namespace Pertamina.WebFIPosting.Application.Masters.Currencys.Command;

public class UpdateCurrencyCommand : CurrencyRequest, IRequest<CurrencyResponse>
{
}
public class UpdateCurrencyCommandValidator : AbstractValidator<UpdateCurrencyCommand>
{
    public UpdateCurrencyCommandValidator()
    {
        RuleFor(x => x.CurrencyCode)
          .NotEmpty()
          .WithMessage("CurrencyCode is Required.");
    }
}

public class UpdateCurrencyCommandHandler : IRequestHandler<UpdateCurrencyCommand, CurrencyResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    public UpdateCurrencyCommandHandler(IWebFiPlatformDbContext context)
    {
        _context = context;
    }
    public async Task<CurrencyResponse> Handle(UpdateCurrencyCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var data = await _context.Currency
                .FirstOrDefaultAsync(x => x.CurrencyCode == request.CurrencyCode, cancellationToken);
            if (data is null)
            {
                throw new NotFoundException($"Currency Code {request.CurrencyCode} not found.");
            }

            data.CurrencyCode = request.CurrencyCode;
            data.LongText = request.LongText;
            data.ShortText = request.ShortText;
            data.ISOCode = request.ISOCode;
            data.AlternativeKey = request.AlternativeKey;
            data.ValidUntil = request.ValidUntil;
            data.PrimarySAPCode = request.PrimarySAPCode;

            await _context.SaveChangesAsync(cancellationToken);

            return new CurrencyResponse { CurrencyCode = data.CurrencyCode };
        }
        catch (Exception ex)
        {
            throw new NotImplementedException("Error while updating Currency", ex);
        }
    }
}
