﻿using Pertamina.WebFIPosting.Domain.Abstracts;

namespace Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;

public class ReverseLineDetail : AuditableEntity
{
    public Guid GeneralLedgerId { get; set; }
    public GeneralLedger GeneralLedger { get; set; } = default!;
    public string? Item { get; set; }
    public string? CompanyCode { get; set; }
    public string? DocumentNumber { get; set; }
    public int? FiscalYear { get; set; }
    public string? ReversalReason { get; set; }
    public DateOnly? PostingDate { get; set; }
    public string? Currency { get; set; }
    public decimal? AmountInDoc { get; set; }
    public string? DocNumberPosted { get; set; }
    public string? DocCurrency { get; set; }
    public int? Year { get; set; }
    public string? PostedAmmount { get; set; }
    public string? Message { get; set; }
    public string? Status { get; set; }
    public string? SimulatedResponseCode { get; set; }
    public string? SimulatedResponseMessage { get; set; }
    public bool IsSimulate { get; set; }
    public DateTime? SimulateDate { get; set; }
}
