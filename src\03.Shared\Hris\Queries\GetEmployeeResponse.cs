﻿namespace Pertamina.WebFIPosting.Shared.Hris.Queries;

public class GetEmployeeResponse
{
    public string Client { get; set; } = default!;
    public string DepartementId { get; set; } = default!;
    public string DepartementName { get; set; } = default!;
    public string DirectoratId { get; set; } = default!;
    public string DirectoratName { get; set; } = default!;
    public string DirektoratPembinaId { get; set; } = default!;
    public string DirektoratPembinaName { get; set; } = default!;
    public string EmployeeAssignedCompanyCode { get; set; } = default!;
    public string EmployeeAssignedCompanyText { get; set; } = default!;
    public string EmployeeAssignedCostCenterId { get; set; } = default!;
    public string EmployeeAssignedCostCenterText { get; set; } = default!;
    public string EmployeeCoorporateEmail { get; set; } = default!;
    public string EmployeeFullName { get; set; } = default!;
    public string EmployeeId { get; set; } = default!;
    public string FungsiId { get; set; } = default!;
    public string FungsiName { get; set; } = default!;
    public bool? IsHeadOfOrganization { get; set; } = default!;
    public string Kbo { get; set; } = default!;
    public string PositionId { get; set; } = default!;
    public string PositionName { get; set; } = default!;
}
