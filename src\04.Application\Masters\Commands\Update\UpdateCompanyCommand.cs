﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Shared.Masters.Company.Commands.Update;
using Pertamina.WebFIPosting.Shared.Masters.Company.Constants;

namespace Pertamina.WebFIPosting.Application.Masters.Commands.Update;
public class UpdateCompanyCommand : UpdateCompanyRequest, IRequest<UpdateCompanyResponse>
{
}

public class UpdateCompanyCommandValidator : AbstractValidator<UpdateCompanyCommand>
{
    public UpdateCompanyCommandValidator()
    {

    }
}

public class UpdateCompanyCommandHandler : IRequestHandler<UpdateCompanyCommand, UpdateCompanyResponse>
{
    private readonly IWebFiPlatformDbContext _context;

    public UpdateCompanyCommandHandler(IWebFiPlatformDbContext context)
    {
        _context = context;
    }

    public async Task<UpdateCompanyResponse> Handle(UpdateCompanyCommand request, CancellationToken cancellationToken)
    {
        var query = await _context.Company.Where(x => x.Code == request.Code).FirstOrDefaultAsync(cancellationToken);

        if (query is null)
        {
            throw new NotFoundException(DisplayTextFor.Company, request.Code);
        }

        return new UpdateCompanyResponse()
        {
            Code = request.Code,
        };
    }
}
