﻿using FluentValidation;
using MediatR;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Masters;
using Pertamina.WebFIPosting.Shared.Masters.Company.Commands.Create;

namespace Pertamina.WebFIPosting.Application.Masters.Commands.Create;
public class CreateCompanyCommand : CreateCompanyRequest, IRequest<CreateCompanyResponse>
{

}

public class CreateCompanyCommandValidator : AbstractValidator<CreateCompanyCommand>
{
    public CreateCompanyCommandValidator()
    {

    }
}

public class CreateCompanyCommandHandler : IRequestHandler<CreateCompanyCommand, CreateCompanyResponse>
{
    private readonly IWebFiPlatformDbContext _context;

    public CreateCompanyCommandHandler(IWebFiPlatformDbContext context)
    {
        _context = context;
    }
    public async Task<CreateCompanyResponse> Handle(CreateCompanyCommand request, CancellationToken cancellationToken)
    {
        var company = new Company()
        {
            Code = request.Code,
            Name = request.Name
        };

        await _context.Company.AddAsync(company);
        await _context.SaveChangesAsync(this, cancellationToken);

        return new CreateCompanyResponse()
        {
            Code = company.Code
        };
    }
}
