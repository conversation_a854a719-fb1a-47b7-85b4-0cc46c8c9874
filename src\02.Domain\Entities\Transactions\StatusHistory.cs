﻿using Pertamina.WebFIPosting.Base.Enums;
using Pertamina.WebFIPosting.Domain.Abstracts;
using Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;

namespace Pertamina.WebFIPosting.Domain.Entities.Transactions;

public class StatusHistory : AuditableEntity
{
    public RequestStatus Status { get; set; } = default!;
    public string StatusType { get; set; } = default!;
    public string? Notes { get; set; } = default!;
    public int TotalApproval { get; set; } = 0;
    public Guid GeneralLedgerId { get; set; }
    public GeneralLedger GeneralLedger { get; set; } = default!;
    public ICollection<StatusComment> StatusComments { get; set; } = default!;
}
