﻿namespace Pertamina.WebFIPosting.Shared.Common.Enums;

public class CountryEnum
{
    public enum ContinentCode
    {
        ASIA,
        AFRICA,
        NORT_AMERICA,
        SOUTH_AMERICA,
        ANTARTICA,
        EUROPA,
        AUSTRALIA
    }

    public static string GetContinentCode(string code)
    {
        if (string.IsNullOrEmpty(code))
        {
            return null;
        }

        // Normalize input (remove spaces, convert to uppercase)
        var normalizedCode = code.ToUpper().Replace(" ", "_");

        // Try direct enum parse first
        if (Enum.TryParse<ContinentCode>(normalizedCode, out var continentEnum))
        {
            return continentEnum.ToString();
        }

        // Handle common variants
        return normalizedCode switch
        {
            "ASIA" => ContinentCode.ASIA.ToString(),
            "AFRICA" => ContinentCode.AFRICA.ToString(),
            "NORTH_AMERICA" or "NORT_AMERICA" => ContinentCode.NORT_AMERICA.ToString(),
            "SOUTH_AMERICA" => ContinentCode.SOUTH_AMERICA.ToString(),
            "ANTARTICA" or "ANTARCTICA" => ContinentCode.ANTARTICA.ToString(),
            "EUROPA" or "EUROPE" => ContinentCode.EUROPA.ToString(),
            "AUSTRALIA" or "AUSTRALIA/OCEANIA" or "OCEANIA" => ContinentCode.AUSTRALIA.ToString(),
            _ => null,
        };
    }

    public static string GetContinentName(string code)
    {
        return code.ToUpper() switch
        {
            "ASIA" => "Asia",
            "AFRICA" => "Africa",
            "NORT_AMERICA" => "North America",
            "SOUTH_AMERICA" => "South America",
            "ANTARTICA" => "Antartica",
            "EUROPA" => "Europa",
            "AUSTRALIA" => "Australia",
            _ => null,
        };
    }
}
