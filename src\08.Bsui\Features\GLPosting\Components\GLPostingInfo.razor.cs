using Microsoft.AspNetCore.Components;
using Pertamina.WebFIPosting.Base.Enums;
using Pertamina.WebFIPosting.Domain.Entities.Transactions;
using Pertamina.WebFIPosting.Shared.Transactions.Statuses.Command;

namespace Pertamina.WebFIPosting.Bsui.Features.GLPosting.Components;

public partial class GLPostingInfo
{
    [Parameter]
    public int? NotifCount { get; set; }
    [Parameter]
    public string GlNumber { get; set; } = default!;
    [Parameter]
    public RequestStatus? Status { get; set; }

    [Parameter]
    public List<StatusHistory>? StatusHistory { get; set; }

    [Parameter]
    public EventCallback<StatusCommentRequest> AddComment { get; set; }

    private bool _open = false;
    private bool _open2;
    public string _comment = default!;

    private async Task SendComment(StatusHistory arg)
    {
        var request = new StatusCommentRequest()
        {
            StatusHistoryId = arg.Id,
            Comment = _comment
        };
        await AddComment.InvokeAsync(request);
    }

    private void ToggleOpen()
    {
        _open = !_open;

        // Pastikan popover lainnya tertutup
        if (_open)
        {
            _open2 = false;
        }

        StateHasChanged();
    }

    //private async Task AddComment()
    //{
    //    var re
    //    var response = await _statusService.CreateStatusCommentAsync()
    //}
    private void ClosePopover()
    {
        _open = false;
    }
}
