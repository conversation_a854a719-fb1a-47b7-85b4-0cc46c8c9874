﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Extensions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Masters;
using Pertamina.WebFIPosting.Shared.Common.Responses;
using Pertamina.WebFIPosting.Shared.Masters.MemberSGPFinanceRRs.Queries;

namespace Pertamina.WebFIPosting.Application.Masters.MemberSGPFinanceRRs.Queries;

public class GetMemberSGPFinanceRRQuery : GetMemberSGPFinanceRRRequest, IRequest<ListResponse<GetMemberSGPFinanceRRResponse>>
{
}
public class GetMemberSGPFinanceRRQueryMapper : IMapFrom<MemberSGPFinanceRR, GetMemberSGPFinanceRRResponse>
{
}

public class GetMemberSGPFinanceRRQueryHandler : IRequestHandler<GetMemberSGPFinanceRRQuery, ListResponse<GetMemberSGPFinanceRRResponse>>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;
    public GetMemberSGPFinanceRRQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }
    public async Task<ListResponse<GetMemberSGPFinanceRRResponse>> Handle(GetMemberSGPFinanceRRQuery request, CancellationToken cancellationToken)
    {
        var members = await _context.MemberSGPFinanceRR
            .Where(x => x.SupportGroupName == request.SupportGroupName)
            .ProjectTo<GetMemberSGPFinanceRRResponse>(_mapper.ConfigurationProvider)
            .ToListAsync();

        return members.ToListResponse();
    }
}
