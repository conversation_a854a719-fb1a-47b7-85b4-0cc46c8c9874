﻿namespace Pertamina.WebFIPosting.Shared.Common.Enums;

public class ViewTypeEnum
{
    public enum ViewTypeCode
    {
        SUMMARY,
        TREE
    }
    public static string GetViewTypeCode(string code)
    {
        if (string.IsNullOrEmpty(code))
        {
            return null;
        }

        if (Enum.TryParse<ViewTypeCode>(code, out var viewTypeCode))
        {
            return viewTypeCode.ToString();
        }

        return code switch
        {
            "SUMMARY" => ViewTypeCode.SUMMARY.ToString(),
            "TREE" => ViewTypeCode.TREE.ToString(),
            _ => null,
        };
    }

    public static string GetInstitutionTypeName(string code)
    {
        return code.ToUpper() switch
        {
            "SUMMARY" => "Summary",
            "TREE" => "Tree",
            _ => null,
        };
    }
}
