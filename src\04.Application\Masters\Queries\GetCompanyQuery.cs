﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Extensions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Masters;
using Pertamina.WebFIPosting.Shared.Common.Responses;
using Pertamina.WebFIPosting.Shared.Masters.Company.Queries;

namespace Pertamina.WebFIPosting.Application.Masters.Queries;
public class GetCompanyQuery : IRequest<ListResponse<GetCompanyQueryResponse>>
{
}

public class GetCompanyQueryMapping : IMapFrom<Company, GetCompanyQueryResponse>
{
    public void Mapping(Profile profile)
    {
        if (profile is not null)
        {
            profile.CreateMap<Company, GetCompanyQueryResponse>();
        }
    }
}

public class GetCompanyQueryHandler : IRequestHandler<GetCompanyQuery, ListResponse<GetCompanyQueryResponse>>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;

    public GetCompanyQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<ListResponse<GetCompanyQueryResponse>> Handle(GetCompanyQuery request, CancellationToken cancellationToken)
    {
        var query = await _context.Company
            .AsNoTracking()
            .Where(x => !x.IsDeleted)
            .OrderBy(x => x.Name)
            .ProjectTo<GetCompanyQueryResponse>(_mapper.ConfigurationProvider)
            .ToListAsync(cancellationToken);

        return query.ToListResponse();
    }
}
