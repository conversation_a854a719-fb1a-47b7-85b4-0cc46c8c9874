﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Shared.Masters.Currency.Command;

namespace Pertamina.WebFIPosting.Application.Masters.Currencys.Command;

public class DeleteCurrencyCommand : IRequest<CurrencyResponse>
{
    public string CurrencyCode { get; set; } = default!;
}
public class DeleteCurrencyCommandValidator : AbstractValidator<DeleteCurrencyCommand>
{
    public DeleteCurrencyCommandValidator()
    {
        RuleFor(x => x.CurrencyCode)
            .NotEmpty()
            .WithMessage("Currency Code is Required.");
    }
}

public class DeleteCurrencyCommandHandler : IRequestHandler<DeleteCurrencyCommand, CurrencyResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    public DeleteCurrencyCommandHandler(IWebFiPlatformDbContext context)
    {
        _context = context;
    }
    public async Task<CurrencyResponse> Handle(DeleteCurrencyCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var data = await _context.Currency
                .FirstOrDefaultAsync(x => x.CurrencyCode == request.CurrencyCode);
            if (data is null)
            {
                throw new NotFoundException($"Currency with CurrencyCode {request.CurrencyCode} not found.");
            }

            _context.Currency.Remove(data);
            await _context.SaveChangesAsync(cancellationToken);

            return new CurrencyResponse { CurrencyCode = request.CurrencyCode };
        }
        catch (Exception ex)
        {
            throw new NotImplementedException("Error while deleting Currency", ex);
        }
    }
}
