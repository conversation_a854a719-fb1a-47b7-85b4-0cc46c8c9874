﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Masters;
using Pertamina.WebFIPosting.Shared.Masters.MasterValidationApprovalRRs.Queries;

namespace Pertamina.WebFIPosting.Application.Masters.MasterValidationApprovalRRs.Queries;

public class GetMasterValidationApprovalRRQuery : GetMasterValidationApprovalRRRequest, IRequest<GetMasterValidationApprovalRRResponse>
{
}
public class GetMasterValidationApprovalRRQueryMapper : IMapFrom<MasterValidationApprovalRR, GetMasterValidationApprovalRRResponse>
{
}
public class GetMasterValidationApprovalRRQueryHandler : IRequestHandler<GetMasterValidationApprovalRRQuery, GetMasterValidationApprovalRRResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;
    public GetMasterValidationApprovalRRQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }
    public async Task<GetMasterValidationApprovalRRResponse> Handle(GetMasterValidationApprovalRRQuery request, CancellationToken cancellationToken)
    {
        var query = await _context.MasterValidationApprovalRR
            .Where(x => x.CompanyCode == request.CompanyCode)
            .Where(x => x.Currency == request.Currency)
            .Where(x => request.ValueMax >= x.NilaiAwal && request.ValueMax <= x.NilaiAkhir)
            .ProjectTo<GetMasterValidationApprovalRRResponse>(_mapper.ConfigurationProvider)
            .FirstOrDefaultAsync(cancellationToken);

        if (query == null)
        {
            throw new NotFoundException("Data not found for the specified criteria.");
        }

        return query;
    }
}
