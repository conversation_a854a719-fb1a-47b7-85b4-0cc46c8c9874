﻿using Pertamina.WebFIPosting.Shared.Common.Attributes;
using Pertamina.WebFIPosting.Shared.Common.Constants;

namespace Pertamina.WebFIPosting.Shared.Transactions.Statuses.Command;

public class StatusCommentRequest
{
    [OpenApiContentType(ContentTypes.TextPlain)]
    public Guid? Id { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public Guid StatusHistoryId { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string Comment { get; set; } = default!;
}
