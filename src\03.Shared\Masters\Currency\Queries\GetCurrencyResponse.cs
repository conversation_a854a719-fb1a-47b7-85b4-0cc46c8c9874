﻿using Pertamina.WebFIPosting.Shared.Common.Responses;

namespace Pertamina.WebFIPosting.Shared.Masters.Currency.Queries;

public class GetCurrencyResponse : Response
{
    public string CurrencyCode { get; set; } = default!;
    public string? LongText { get; set; } = default!;
    public string? ShortText { get; set; } = default!;
    public string? ISOCode { get; set; } = default!;
    public string? AlternativeKey { get; set; } = default!;
    public string? ValidUntil { get; set; } = default!;
    public string? PrimarySAPCode { get; set; } = default!;
}
