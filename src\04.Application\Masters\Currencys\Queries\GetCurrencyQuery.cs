﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Extensions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Masters;
using Pertamina.WebFIPosting.Shared.Common.Responses;
using Pertamina.WebFIPosting.Shared.Masters.Currency.Queries;

namespace Pertamina.WebFIPosting.Application.Masters.Currencys.Queries;

public class GetCurrencyQuery : IRequest<ListResponse<GetCurrencyResponse>>
{
}
public class GetCurrencyQueryMapper : IMapFrom<Currency, GetCurrencyResponse> { }

public class GetCurrencyQueryHandler : IRequestHandler<GetCurrencyQuery, ListResponse<GetCurrencyResponse>>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;
    public GetCurrencyQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }
    public async Task<ListResponse<GetCurrencyResponse>> Handle(GetCurrencyQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var query = await _context.Currency
                .ProjectTo<GetCurrencyResponse>(_mapper.ConfigurationProvider)
                .ToListAsync(cancellationToken);

            return query.ToListResponse();
        }
        catch (Exception ex)
        {
            throw new NotImplementedException();
        }
    }
}
