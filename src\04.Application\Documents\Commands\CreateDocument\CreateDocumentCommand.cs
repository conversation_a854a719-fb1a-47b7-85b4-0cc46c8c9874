﻿using FluentValidation;
using MediatR;
using Microsoft.Extensions.Options;
using Pertamina.WebFIPosting.Application.Common.Attributes;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Application.Services.Storage;
using Pertamina.WebFIPosting.Domain.Entities;
using Pertamina.WebFIPosting.Domain.Events;
using Pertamina.WebFIPosting.Shared.Documents.Commands.CreateDocument;
using Pertamina.WebFIPosting.Shared.Documents.Options;
using Pertamina.WebFIPosting.Shared.Services.Authorization.Constants;

namespace Pertamina.WebFIPosting.Application.Documents.Commands.CreateDocument;

[Authorize(Policy = Permissions.WebFi_Api_Audience)]
public class CreateDocumentCommand : CreateDocumentRequest, IRequest<CreateDocumentResponse>
{
}

public class CreateDocumentCommandValidator : AbstractValidator<CreateDocumentCommand>
{
    public CreateDocumentCommandValidator(IOptions<DocumentOptions> documentOptions)
    {
        Include(new CreateDocumentRequestValidator(documentOptions));
    }
}

public class CreateDocumentCommandHandler : IRequestHandler<CreateDocumentCommand, CreateDocumentResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IStorageService _storageService;

    public CreateDocumentCommandHandler(
        IWebFiPlatformDbContext context,
        IStorageService storageService)
    {
        _context = context;
        _storageService = storageService;
    }

    public async Task<CreateDocumentResponse> Handle(CreateDocumentCommand request, CancellationToken cancellationToken)
    {
        using var memoryStream = new MemoryStream();
        await request.File.CopyToAsync(memoryStream, cancellationToken);
        memoryStream.Position = 0;
        var file = memoryStream.ToArray();

        var document = new Document
        {
            Id = Guid.NewGuid(),
            CompanyCode = "1010",
            DocumentCategoryName = "Penghargaan",
            JrdpYear = 2010,
            TopicCode = "S9.03",
            Title = request.Title,
            FileName = request.File.FileName,
            FileSize = request.File.Length,
            FileContentType = request.File.ContentType,
            StorageFileId = await _storageService.CreateAsync(file)
        };

        document.DomainEvents.Add(new DocumentCreatedEvent(document.Id));

        await _context.Documents.AddAsync(document, cancellationToken);
        await _context.SaveChangesAsync(this, cancellationToken);

        return new CreateDocumentResponse
        {
            DocumentId = document.Id
        };
    }
}
