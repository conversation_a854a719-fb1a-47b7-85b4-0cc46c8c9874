﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;
using Pertamina.WebFIPosting.Shared.Transactions.GLPostingClearingLineDetails.Commands;

namespace Pertamina.WebFIPosting.Application.Transactions.GLPostingClearingLineDetails.Commands;
public class CreateGLPostingClearingLineDetailsCommand : GLPostingClearingLineDetailRequest, IRequest<GLPostingClearingLineDetailResponse>
{
}

public class CreateGLPostingClearingLineDetailsCommandValidator : AbstractValidator<CreateGLPostingClearingLineDetailsCommand>
{
    public CreateGLPostingClearingLineDetailsCommandValidator()
    {
        RuleFor(x => x.GeneralLedgerId)
           .NotEmpty()
           .WithMessage("General Ledger ID is required.");
    }
}

public class CreateGLPostingClearingLineDetailsCommandHandler : IRequestHandler<CreateGLPostingClearingLineDetailsCommand, GLPostingClearingLineDetailResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    public CreateGLPostingClearingLineDetailsCommandHandler(IWebFiPlatformDbContext context)
    {
        _context = context;
    }

    public async Task<GLPostingClearingLineDetailResponse> Handle(CreateGLPostingClearingLineDetailsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var generalLedgerExists = await _context.GeneralLedger
                .AnyAsync(x => x.Id == request.GeneralLedgerId, cancellationToken);

            if (!generalLedgerExists)
            {
                throw new NotFoundException($"General Ledger with ID {request.Id} not found.");
            }

            var setData = new GLPostingClearingLineDetail
            {
                GeneralLedgerId = request.GeneralLedgerId,
                Item = request.Item,
                CompanyCode = request.CompanyCode,
                DocumentDate = request.DocumentDate,
                PostingDate = request.PostingDate,
                Period = request.Period,
                TranslationDate = request.TranslationDate,
                DebitCredit = request.DebitCredit,
                AccountType = request.AccountType,
                GLAccount = request.GLAccount,
                Reference = request.Reference,
                DocHeaderText = request.DocHeaderText,
                DocType = request.DocType,
                Currency = request.Currency,
                AmountinDoc = request.AmountinDoc,
                AmountinLC = request.AmountinLC,
                AmountinLC2 = request.AmountinLC2,
                AmountinLC3 = request.AmountinLC3,
                Text = request.Text,
                TaxCode = request.TaxCode,
                Assignment = request.Assignment,
                CostCenter = request.CostCenter,
                Plant = request.Plant,
                ProfitCenter = request.ProfitCenter,
                InternalOrder = request.InternalOrder,
                WBS = request.WBS,
                BussinessPlace = request.BussinessPlace,
                Rec_Indicator = request.Rec_Indicator,
                Reference_Key_1 = request.Reference_Key_1,
                Reference_Key_2 = request.Reference_Key_2,
                Reference_Key_3 = request.Reference_Key_3,
                Trading_Partner = request.Trading_Partner,
                TransactnType = request.TransactnType,
                NoDocumentClearing = request.NoDocumentClearing,
                YearDocumentClearing = request.YearDocumentClearing,
                AmountDocumentClearing = request.AmountDocumentClearing,
                DocNumberPosted = request.DocNumberPosted,
                DocCurrency = request.DocCurrency,
                Year = request.Year,
                PostedAmmount = request.PostedAmmount,
                Message = request.Message,
                Status = request.Status,
                SimulatedResponseCode = request.SimulatedResponseCode,
                SimulatedResponseMessage = request.SimulatedResponseMessage,
                IsSimulate = request.IsSimulate,
                SimulateDate = request.SimulateDate
            };

            await _context.GLPostingClearingLineDetail.AddAsync(setData, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            return new GLPostingClearingLineDetailResponse() { Id = setData.Id };
        }
        catch (Exception ex)
        {
            throw new NotImplementedException("Error while creating GLPosting Clearing Line Detail", ex);
        }
    }
}
