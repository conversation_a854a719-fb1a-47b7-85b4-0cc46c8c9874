﻿using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgerLineDetails.Commands;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgers.Commands;

namespace Pertamina.WebFIPosting.Application.Services.Sap;

public interface ISapService
{
    public Task<List<GeneralLedgerLineDetailRequest>> SimulateGLPostingsync(IEnumerable<GeneralLedgerLineDetailRequest> requests, GeneralLedgerRequest requestHeader, IWebFiPlatformDbContext context);
}
