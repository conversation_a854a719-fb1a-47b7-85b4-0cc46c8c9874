﻿using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Domain.Entities;
using Pertamina.WebFIPosting.Domain.Entities.AuditService;
using Pertamina.WebFIPosting.Domain.Entities.Masters;
using Pertamina.WebFIPosting.Domain.Entities.Transactions;
using Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;

namespace Pertamina.WebFIPosting.Application.Services.Persistence;

public interface IWebFiPlatformDbContext
{
    #region Essential Entities

    public DbSet<Audit> Audits { get; }

    #endregion Essential Entities

    #region Business Entities

    public DbSet<Document> Documents { get; }

    #endregion Business Entities

    #region Master Entities
    public DbSet<LogServiceIntegration> LogServiceIntegration { get; }
    public DbSet<Company> Company { get; }
    public DbSet<MasterCompanyCodeClient> MasterCompanyCodeClient { get; }
    public DbSet<MasterCompanyCodeCurency> MasterCompanyCodeCurency { get; }

    public DbSet<ZTBCOCDCLIENT> ZTBCOCDCLIENT { get; }

    public DbSet<ZTBCOCDCURR> ZTBCOCDCURR { get; }
    public DbSet<MasterValidationApprovalRR> MasterValidationApprovalRR { get; }
    public DbSet<MemberSGPFinanceRR> MemberSGPFinanceRR { get; }
    public DbSet<Currency> Currency { get; }

    //public DbSet<ZTCURRTEST> ZTCURRTEST { get; }

    //public DbSet<ZTFI_COCDCURR> ZTFI_COCDCURR { get; }

    //public DbSet<ZTFI_COCURTEST> ZTFI_COCURTEST { get; }

    #endregion

    #region Transactional Entities
    public DbSet<StatusHistory> StatusHistory { get; }
    public DbSet<StatusComment> StatusComment { get; }
    public DbSet<GeneralLedger> GeneralLedger { get; }
    public DbSet<GeneralLedgerFile> GeneralLedgerFile { get; }
    public DbSet<GeneralLedgerLineDetail> GeneralLedgerLineDetail { get; }
    public DbSet<GLPostingClearingLineDetail> GLPostingClearingLineDetail { get; }
    public DbSet<ManualBankStatementLineDetail> ManualBankStatementLineDetail { get; }
    public DbSet<RecurringLineDetail> RecurringLineDetail { get; }
    public DbSet<ResetReversDocumentLineDetail> ResetReversDocumentLineDetail { get; }
    public DbSet<ReverseLineDetail> ReverseLineDetail { get; }

    #endregion

    public Task<int> SaveChangesAsync<THandler>(THandler handler, CancellationToken cancellationToken = default) where THandler : notnull;
}
