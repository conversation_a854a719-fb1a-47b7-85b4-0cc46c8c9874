﻿using MediatR;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgers.Commands;

namespace Pertamina.WebFIPosting.Application.Transactions.GeneralLedgers.Commands;

public class UpdateWoNumberGeneralLedgerCommand : GeneralLedgerWoNumberRequest, IRequest<GeneralLedgerResponse>
{
}
public class UpdateWoNumberGeneralLedgerCommandHandler : IRequestHandler<UpdateWoNumberGeneralLedgerCommand, GeneralLedgerResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    public UpdateWoNumberGeneralLedgerCommandHandler(IWebFiPlatformDbContext context)
    {
        _context = context;
    }
    public async Task<GeneralLedgerResponse> Handle(UpdateWoNumberGeneralLedgerCommand request, CancellationToken cancellationToken)
    {
        var query = await _context.GeneralLedger.FindAsync(request.Id, cancellationToken);
        if (query == null)
        {
            throw new NotFoundException("General Ledger not found.");
        }

        query.WorkOrderNo = request.WoNumber;
        await _context.SaveChangesAsync(cancellationToken);

        return new GeneralLedgerResponse() { Id = query.Id };
    }
}
