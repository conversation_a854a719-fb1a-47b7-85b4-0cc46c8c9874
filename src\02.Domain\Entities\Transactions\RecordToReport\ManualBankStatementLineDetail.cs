﻿using Pertamina.WebFIPosting.Domain.Abstracts;

namespace Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;

public class ManualBankStatementLineDetail : AuditableEntity
{
    public Guid GeneralLedgerId { get; set; }
    public GeneralLedger GeneralLedger { get; set; } = default!;
    public string? Item { get; set; }
    public string? DocHeaderText { get; set; }
    public string? TransactionType { get; set; }
    public string? TanggalText { get; set; }
    public string? Assignment { get; set; }
    public string? Currency { get; set; }
    public string? OpeningBalance { get; set; }
    public string? UangMasuk { get; set; }
    public string? UangKeluar { get; set; }
    public string? EndingBalance { get; set; }
    public string? DocNumberPosted { get; set; }
    public string? DocCurrency { get; set; }
    public int? Year { get; set; }
    public string? PostedAmmount { get; set; }
    public string? Message { get; set; }
    public string? Status { get; set; }
    public string? SimulatedResponseCode { get; set; }
    public string? SimulatedResponseMessage { get; set; }
    public bool IsSimulate { get; set; }
    public DateTime? SimulateDate { get; set; }
}
