﻿using MediatR;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Application.Services.Storage;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgerFiles.Queries;

namespace Pertamina.WebFIPosting.Application.Transactions.GeneralLedgerFiles.Commands;
public class UpdateGeneralLedgerFileCommand : GetGeneralLedgerFileRequest, IRequest<GetGeneralLedgerFileResponse>
{
    public Guid Id { get; set; }
}
public class UpdateGeneralLedgerFileCommandHandler : IRequestHandler<UpdateGeneralLedgerFileCommand, GetGeneralLedgerFileResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IStorageService _storageService;
    public UpdateGeneralLedgerFileCommandHandler(IWebFiPlatformDbContext context, IStorageService storageService)
    {
        _context = context;
        _storageService = storageService;
    }

    public async Task<GetGeneralLedgerFileResponse> Handle(UpdateGeneralLedgerFileCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var generalLedgerFile = await _context.GeneralLedgerFile.FindAsync(request.Id);
            if (generalLedgerFile == null)
            {
                throw new NotFoundException("General Ledger File not found.");
            }

            if (request.Files != null && request.Files.Any())
            {
                var oldStorageFileId = generalLedgerFile.StorageFileId;

                foreach (var file in request.Files)
                {
                    using var memoryStream = new MemoryStream();
                    await file.CopyToAsync(memoryStream, cancellationToken);
                    memoryStream.Position = 0;
                    var fileData = memoryStream.ToArray();

                    var newStorageFileId = await _storageService.CreateAsync(fileData);

                    generalLedgerFile.GeneralLedgerId = request.GeneralLedgerId;
                    generalLedgerFile.FileName = file.FileName;
                    generalLedgerFile.FileSize = file.Length;
                    generalLedgerFile.FileContentType = file.ContentType;
                    generalLedgerFile.StorageFileId = newStorageFileId;

                    _context.GeneralLedgerFile.Update(generalLedgerFile);
                }

                await _context.SaveChangesAsync(cancellationToken);

                if (!string.IsNullOrEmpty(oldStorageFileId))
                {
                    try
                    {
                        await _storageService.DeleteAsync(oldStorageFileId);
                    }
                    catch (Exception ex)
                    {
                        throw new NotImplementedException("Warning: Failed to delete old storage file: ", ex);
                    }
                }
            }

            return new GetGeneralLedgerFileResponse();
        }
        catch (Exception ex)
        {
            throw new ApplicationException("Error while updating General Ledger File", ex);
        }
    }
}
