@using System.Net.Http.Json
@using Pertamina.WebFIPosting.Bsui.Services.AppInfo
@inject IJSRuntime JSRuntime
@using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgers.Queries
@using Syncfusion.Blazor.SplitButtons
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.DropDowns

<style>
	.mud-grid-spacing-xs-3 > .mud-grid-item {
	padding-left: 12px;
	padding-top: 2px;
	}

	.no-uppercase {
	text-transform: none;
	white-space: nowrap;
	}

	.e-badge-danger {
	background-color: #f8d7da;
	color: #721c24;
	border-radius: 5px;
	padding: 3px 8px;
	}

	.e-badge-info {
	background-color: #d1ecf1;
	color: #0c5460;
	border-radius: 5px;
	padding: 3px 8px;
	}

	.custom-badge {
	background-color: #c3c3c3 !important;
	color: white !important; /* Atur warna teks jika perlu */
	}

	.mud-tab {
	text-transform: none;
	}
</style>

<MudGrid Spacing="3" Class="mt-4">
	<MudItem xs="12" md="12">
		<MudPaper Class="pa-4 mb-4" Elevation="2">
			<MudGrid Spacing="2" Class="mb-4">
				<MudItem xs="12" md="7" Style="">
					<MudGrid Class="d-flex align-center justify-start">
						<MudItem>
							<MudText Typo="Typo.h6">Line Item</MudText>
						</MudItem>
					</MudGrid>
				</MudItem>
			</MudGrid>
            <MudGrid GutterSize="3" Class="mb-6">
				<!-- Form Input -->
				<MudItem xs="12" md="12">
					<MudPaper Class="pa-0 d-flex flex-column" Style="height:100%;">
						<MudItem xs="12" md="12">
							<MudGrid AlignItems="center" Class="pa-1 mb-2">
                                <MudItem xs="12" md="4">
                                    <MudGrid Class="d-flex justify-start">
                                        <MudItem xs="12">
                                            <MudText Typo="Typo.body2" Class="mb-2 fw-bold">Request Period <span style="color: red; font-size: 0.8em;">*</span></MudText>
                                            <SfDateRangePicker ID="ddlDocDate" TValue="DateTime?" FullScreen="false" ShowClearButton="true" CssClass="e-outline e-small" Placeholder="Choose a Date"></SfDateRangePicker>
                                        </MudItem>
                                    </MudGrid>
                                </MudItem>
                                <MudItem xs="12" md="4">
                                    <MudGrid Class="d-flex justify-start">
                                        <MudItem xs="12">
                                            <MudText Typo="Typo.body2" Class="mb-2 fw-bold">Posting Period Date <span style="color: red; font-size: 0.8em;">*</span></MudText>
                                            <SfDropDownList TValue="string" TItem="string" CssClass="e-outline e-small" Placeholder="Select Posting Period Date" @bind-Value="_request.CompanyCodeTransactionId" AllowFiltering="true" />
                                        </MudItem>
                                    </MudGrid>
                                </MudItem>
                                <MudItem xs="12" md="4">
                                    <MudGrid Class="d-flex justify-start">
                                        <MudItem xs="12">
                                            <MudText Typo="Typo.body2" Class="mb-2 fw-bold">No. Ticket Web <span style="color: red; font-size: 0.8em;">*</span></MudText>
                                            <SfTextBox CssClass="e-outline e-small" Placeholder="Type Here"></SfTextBox>
                                        </MudItem>
                                    </MudGrid>
                                </MudItem>
							</MudGrid>
                            <MudGrid AlignItems="center" Class="pa-1 mb-2">
                                <MudItem xs="12" md="4">
                                    <MudGrid Class="d-flex justify-start">
                                        <MudItem xs="12">
                                            <MudText Typo="Typo.body2" Class="mb-2 fw-bold">Amount <span style="color: red; font-size: 0.8em;">*</span></MudText>
                                            <SfTextBox CssClass="e-outline e-small" Placeholder="Type Here"></SfTextBox>
                                        </MudItem>
                                    </MudGrid>
                                </MudItem>
                                <MudItem xs="12" md="4">
                                    <MudGrid Class="d-flex justify-start">
                                        <MudItem xs="12">
                                            <MudText Typo="Typo.body2" Class="mb-2 fw-bold">GL <span style="color: red; font-size: 0.8em;">*</span></MudText>
                                            <SfTextBox CssClass="e-outline e-small" Placeholder="Type Here"></SfTextBox>
                                        </MudItem>
                                    </MudGrid>
                                </MudItem>
                                <MudItem xs="12" md="4">
                                    <MudGrid Class="d-flex justify-start">
                                        <MudItem xs="12">
                                            <MudText Typo="Typo.body2" Class="mb-2 fw-bold">Operational Category 3 <span style="color: red; font-size: 0.8em;">*</span></MudText>
                                            <SfDropDownList TValue="string" TItem="string" CssClass="e-outline e-small" Placeholder="Select Operational Category 3" @bind-Value="_request.CompanyCodeTransactionId" AllowFiltering="true" />
                                        </MudItem>
                                    </MudGrid>
                                </MudItem>
                            </MudGrid>
                            <MudGrid AlignItems="center" Class="pa-1 mb-2">
                                <MudItem xs="12" md="4">
                                    <MudGrid Class="d-flex justify-start">
                                        <MudItem xs="12">
                                            <MudText Typo="Typo.body2" Class="mb-2 fw-bold">No Doc SAP <span style="color: red; font-size: 0.8em;">*</span></MudText>
                                            <SfTextBox CssClass="e-outline e-small" Placeholder="Type Here"></SfTextBox>
                                        </MudItem>
                                    </MudGrid>
                                </MudItem>
                                <MudItem xs="12" md="4">
                                    <MudGrid Class="d-flex justify-start">
                                        <MudItem xs="12">
                                            <MudText Typo="Typo.body2" Class="mb-2 fw-bold">Debit/Credit Indicator <span style="color: red; font-size: 0.8em;">*</span></MudText>
                                            <SfDropDownList TValue="string" TItem="string" CssClass="e-outline e-small" Placeholder="Select Debit/Credit" @bind-Value="_request.CompanyCodeTransactionId" AllowFiltering="true" />
                                        </MudItem>
                                    </MudGrid>
                                </MudItem>
                            </MudGrid>
						</MudItem>
					</MudPaper>
				</MudItem>
			</MudGrid>

			<!-- Table -->
			@if (_isLoadingData)
			{
				<LoadingLinear IsVisible="_isLoadingData" />
			}
			else
			{
                <MudGrid Spacing="2" Class="mb-1">
                    <MudItem xs="12" md="12">
                        <MudGrid Class="d-flex flex-wrap align-center justify-start justify-md-end p-1">
                            <MudItem>
                                <SfProgressButton ID="resetButton" Content="Reset All" EnableProgress="true" CssClass="fw-bold mr-3" IconCss="e-icons e-reset" Style="background: linear-gradient(to right, #e53935, #b71c1c); color: white;">
                                    <ProgressButtonSpinSettings Position="SpinPosition.Center"></ProgressButtonSpinSettings>
                                    <ProgressButtonAnimationSettings Effect="Syncfusion.Blazor.SplitButtons.AnimationEffect.ZoomOut"></ProgressButtonAnimationSettings>
                                </SfProgressButton>
                                <SfProgressButton ID="exportButton" Content="Export to Excel" @onclick="ExportExcel" EnableProgress="true" CssClass="fw-bold" IconCss="e-icons e-download" Style="background: linear-gradient(to right, #4279db, #1c4185); color: white;">
                                    <ProgressButtonSpinSettings Position="SpinPosition.Center"></ProgressButtonSpinSettings>
                                    <ProgressButtonAnimationSettings Effect="Syncfusion.Blazor.SplitButtons.AnimationEffect.ZoomOut"></ProgressButtonAnimationSettings>
                                </SfProgressButton>
                            </MudItem>
                        </MudGrid>
                    </MudItem>
                </MudGrid>
				<MudGrid Spacing="2">
					<MudItem xs="12" md="12">
                        <SfGrid @ref="_gridRef" DataSource="@_getGeneralLedgerReponses" Toolbar="@(new List<string>() { "Search" })" AllowExcelExport="true" AllowFiltering="true" AllowPaging="true" AllowSorting="true"
								AllowResizing="true" AllowTextWrap="true">
							<GridPageSettings PageCount="10" PageSize="10" PageSizes="true"></GridPageSettings>
							<GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                            <GridEvents ExcelQueryCellInfoEvent="ExcelQueryCellInfoHandler" OnToolbarClick="ToolbarClickHandler" TValue="GetGeneralLedgerReponse"></GridEvents>
							<GridColumns>
								<GridColumn Field=@nameof(GetGeneralLedgerReponse.TicketNo) HeaderText="No Ticket Web" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left" Width="200"></GridColumn>
								<GridColumn Field=@nameof(GetGeneralLedgerReponse.TicketBMC) HeaderText="No Doc SAP" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left" Width="200"></GridColumn>
								<GridColumn Field=@nameof(GetGeneralLedgerReponse.CompanyCodeTransactionId) HeaderText="GL" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left" Width="150"></GridColumn>
								<GridColumn Field=@nameof(GetGeneralLedgerReponse.WorkOrderNo) HeaderText="Amount" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left" Width="150"></GridColumn>
								<GridColumn Field=@nameof(GetGeneralLedgerReponse.CategoryRequest) HeaderText="Debit/Credit Indicator" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left" Width="180"></GridColumn>
								<GridColumn Field=@nameof(GetGeneralLedgerReponse.SupportGroup) HeaderText="Operational Category 3" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left" Width="200"></GridColumn>
                                <GridColumn Field=@nameof(GetGeneralLedgerReponse.Created) HeaderText="Request Date" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left" Width="200">
									<Template>
										@{
											var dateOnly = (context as GetGeneralLedgerReponse).Created.ToString("dd-MM-yyyy");
											var timeOnly = (context as GetGeneralLedgerReponse).Created.ToString("hh:mm:ss tt");
										}
										<span>@dateOnly <span style="color: #ccc; font-size: 1.2em;">•</span> @timeOnly</span>
									</Template>
								</GridColumn>
								<GridColumn Field=@nameof(GetGeneralLedgerReponse.PostingDate) HeaderText="Posting Date" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left" Width="200">
									<Template>
										@{
											var postingDate = (context as GetGeneralLedgerReponse).PostingDate;
											var formattedDate = postingDate?.ToString("dd-MM-yyyy") ?? "N/A";
										}
										<span>@formattedDate</span>
									</Template>
								</GridColumn>

								<GridColumn HeaderText="Action" Width="150" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center">
									<Template>
										@{
											var item = context as GetGeneralLedgerReponse;
											var deleteButtonClass = item.Status == Base.Enums.RequestStatus.Rejected
											? "e-flat e-default e-small"   // Ganti warna menjadi default jika IsRejected
											: "e-flat e-danger e-small";   // Warna merah jika tidak
										}
										<SfButton CssClass="e-flat e-primary e-small" IconCss="e-icons e-eye" title="View Detail" OnClick="() => DetailGLPosting(item)"></SfButton>
										@* <SfButton CssClass="e-flat e-warning e-small" IconCss="e-icons e-comment-show" title="Information"></SfButton>
										<SfButton CssClass="@deleteButtonClass" IconCss="e-icons e-delete" title="Delete" OnClick="@(async () => await OpenDialogAsyncDelete())"></SfButton> *@
									</Template>
								</GridColumn>
							</GridColumns>
						</SfGrid>
					</MudItem>
				</MudGrid>
			}
		</MudPaper>
	</MudItem>

</MudGrid>
