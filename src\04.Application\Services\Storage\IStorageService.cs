﻿namespace Pertamina.WebFIPosting.Application.Services.Storage;

public interface IStorageService
{
    public Task<string> CreateAsync(byte[] data);
    public Task<byte[]> ReadAsync(string storedFileName);
    public Task<string> ReadAsBase64Async(string storedFileName);
    public Task<bool> DeleteAsync(string storedFileName);
    public Task<string> UpdateAsync(string storedFileName, byte[] newData);
}
