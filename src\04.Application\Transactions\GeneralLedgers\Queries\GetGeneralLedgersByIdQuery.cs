﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgers.Queries;

namespace Pertamina.WebFIPosting.Application.Transactions.GeneralLedgers.Queries;
public class GetGeneralLedgersByIdQuery : IRequest<GetGeneralLedgerReponse>
{
    public Guid Id { get; set; }
}

public class GetGeneralLedgersByIdQueryMapper : IMapFrom<GeneralLedger, GetGeneralLedgerReponse>
{
    public void Mapping(Profile profile)
    {
        if (profile is not null)
        {
            profile.CreateMap<GeneralLedger, GetGeneralLedgerReponse>()
                .ForMember(x => x.GeneralLedgerLineDetails, o => o.MapFrom(s => s.GeneralLedgerLineDetails))
                .ForMember(x => x.StatusHistory, o => o.MapFrom(s => s.StatusHistorys))
                .ForMember(x => x.GeneralLedgerFiles, o => o.MapFrom(s => s.GeneralLedgerFiles))
                ;
        }
    }
}

public class GetGeneralLedgersByIdQueryHandler : IRequestHandler<GetGeneralLedgersByIdQuery, GetGeneralLedgerReponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;
    public GetGeneralLedgersByIdQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }
    public async Task<GetGeneralLedgerReponse> Handle(GetGeneralLedgersByIdQuery request, CancellationToken cancellationToken)
    {
        var query = await _context.GeneralLedger
            .Include(x => x.GeneralLedgerLineDetails)
            .Include(x => x.StatusHistorys)
            .Include(x => x.GeneralLedgerFiles)
            .AsNoTracking()
            .Where(x => !x.IsDeleted && x.Id == request.Id)
            .ProjectTo<GetGeneralLedgerReponse>(_mapper.ConfigurationProvider)
            .FirstOrDefaultAsync(cancellationToken);

        var statusHistory = await _context.StatusHistory.Include(x => x.StatusComments).Where(x => x.GeneralLedgerId == request.Id).ToListAsync();
        query.StatusHistory = statusHistory;
        return query;
    }
}
