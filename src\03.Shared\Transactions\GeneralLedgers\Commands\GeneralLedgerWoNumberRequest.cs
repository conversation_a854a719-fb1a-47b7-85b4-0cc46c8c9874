﻿using Pertamina.WebFIPosting.Shared.Common.Attributes;
using Pertamina.WebFIPosting.Shared.Common.Constants;

namespace Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgers.Commands;

public class GeneralLedgerWoNumberRequest
{
    [OpenApiContentType(ContentTypes.TextPlain)]
    public Guid? Id { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? BmcNumber { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? WoNumber { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateTime? SVTDueDate { get; set; } = default!;
}
