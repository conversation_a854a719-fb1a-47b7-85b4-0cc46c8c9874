﻿using FluentValidation;
using Pertamina.WebFIPosting.Shared.Common.Attributes;
using Pertamina.WebFIPosting.Shared.Common.Constants;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgers.Commands;

namespace Pertamina.WebFIPosting.Shared.Transactions.ResetReversDocumentLineDetails.Commands;
public class ResetReversDocumentLineDetailRequest
{
    [OpenApiContentType(ContentTypes.TextPlain)]
    public Guid? Id { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public Guid GeneralLedgerId { get; set; }

    [OpenApiContentType(ContentTypes.ApplicationJson)]
    public IEnumerable<GeneralLedgerRequest> GeneralLedger { get; set; } = default!;

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Item { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? CompanyCode { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateOnly? DocumentDate { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateOnly? PostingDate { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Period { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateOnly? TranslationDate { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? DebitCredit { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? AccountType { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? GLAccount { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Reference { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? DocHeaderText { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? DocType { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Currency { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? AmountinDoc { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? AmountinLC { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? AmountinLC2 { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? AmountinLC3 { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Text { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? TaxCode { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Assignment { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? CostCenter { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Plant { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? ProfitCenter { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? InternalOrder { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? WBS { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? BussinessPlace { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Rec_Indicator { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Reference_Key_1 { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Reference_Key_2 { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Reference_Key_3 { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Trading_Partner { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? TransactnType { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? NoDocumentClearing { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? YearDocumentClearing { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? AmountDocumentClearing { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? DocNumberPosted { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? DocCurrency { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public int? Year { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? PostedAmmount { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Message { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Status { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? SimulatedResponseCode { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? SimulatedResponseMessage { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public bool IsSimulate { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateTime? SimulateDate { get; set; }
}

public class ResetReversDocumentLineDetailRequestValidator : AbstractValidator<ResetReversDocumentLineDetailRequest>
{
    public ResetReversDocumentLineDetailRequestValidator()
    {
        RuleFor(x => x.GeneralLedgerId)
            .NotEmpty()
            .WithMessage("General Ledger ID is required.");
    }
}
