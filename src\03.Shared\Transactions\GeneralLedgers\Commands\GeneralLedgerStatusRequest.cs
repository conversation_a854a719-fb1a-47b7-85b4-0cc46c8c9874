﻿using Pertamina.WebFIPosting.Base.Enums;
using Pertamina.WebFIPosting.Shared.Common.Attributes;
using Pertamina.WebFIPosting.Shared.Common.Constants;

namespace Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgers.Commands;

public class GeneralLedgerStatusRequest
{
    [OpenApiContentType(ContentTypes.TextPlain)]
    public Guid? Id { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public RequestStatus? Status { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? SupportGroup { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? AssignTo { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? NameAssignTo { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? EmailAssignTo { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? ProffofExecution { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Comment { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? BmcNumber { get; set; } = default!;
}
