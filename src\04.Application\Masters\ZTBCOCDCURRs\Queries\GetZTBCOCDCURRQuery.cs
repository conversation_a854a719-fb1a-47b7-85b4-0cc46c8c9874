﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Masters;
using Pertamina.WebFIPosting.Shared.Common.Responses;
using Pertamina.WebFIPosting.Application.Common.Extensions;
using Pertamina.WebFIPosting.Shared.Masters.ZTBCOCDCURR.Queries;

namespace Pertamina.WebFIPosting.Application.Masters.ZTBCOCDCURRs.Queries;

public class GetZTBCOCDCURRQuery : IRequest<ListResponse<GetZTBCOCDCURRQueryResponse>>
{
}

public class GetZTBCOCDCURRQueryMapping : IMapFrom<ZTBCOCDCURR, GetZTBCOCDCURRQueryResponse>
{
    public void Mapping(Profile profile)
    {
        profile.CreateMap<ZTBCOCDCURR, GetZTBCOCDCURRQueryResponse>();
    }
}

public class GetZTBCOCDCURRQueryHandler : IRequestHandler<GetZTBCOCDCURRQuery, ListResponse<GetZTBCOCDCURRQueryResponse>>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;

    public GetZTBCOCDCURRQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<ListResponse<GetZTBCOCDCURRQueryResponse>> Handle(GetZTBCOCDCURRQuery request, CancellationToken cancellationToken)
    {
        var query = await _context.ZTBCOCDCURR
            .AsNoTracking()
            .Where(x => x.ISDELETED != null)
            .ProjectTo<GetZTBCOCDCURRQueryResponse>(_mapper.ConfigurationProvider)
            .ToListAsync(cancellationToken);

        return query.ToListResponse();
    }
}
