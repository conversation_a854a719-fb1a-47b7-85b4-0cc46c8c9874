﻿using Pertamina.WebFIPosting.Base.Enums;
using Pertamina.WebFIPosting.Domain.Abstracts;

namespace Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;

public class GeneralLedger : AuditableEntity
{
    public string TicketNo { get; set; } = default!;
    public string? TicketBMC { get; set; } = default!;
    public string? WorkOrderNo { get; set; } = default!;
    public DateTime? PostingDate { get; set; } = default!;
    public DateTime? DocumentDate { get; set; } = default!;
    public DateTime? TranslationDate { get; set; } = default!;
    public string? PostingPeriod { get; set; } = default!;
    public string? ClearingText { get; set; } = default!;
    public DateOnly TargetDate { get; set; } = default!;
    public string CompanyCodeRequestorId { get; set; } = default!;
    public string CostCenterRequestor { get; set; } = default!;
    public string JobTitleRequestor { get; set; } = default!;
    public string NameRequestor { get; set; } = default!;
    public string EmailRequestor { get; set; } = default!;
    public string PhoneNumberRequestor { get; set; } = default!;
    public string ApproverPjsVacan { get; set; } = default!;
    public string? Approver1 { get; set; } = default!;
    public string? NameApprover1 { get; set; } = default!;
    public string? EmailApprover1 { get; set; } = default!;
    public string? Approver2 { get; set; } = default!;
    public string? NameApprover2 { get; set; } = default!;
    public string? EmailApprover2 { get; set; } = default!;
    public string? Approver3 { get; set; } = default!;
    public string? NameApprover3 { get; set; } = default!;
    public string? EmailApprover3 { get; set; } = default!;
    public string Metode { get; set; } = default!;
    public string CompanyCodeTransactionId { get; set; } = default!;
    public string Currency { get; set; } = default!;
    public string CurrentPeriod { get; set; } = default!;
    public string? CategoryRequest { get; set; } = default!;
    public string? SupportGroup { get; set; } = default!;
    public string? AssignTo { get; set; } = default!;
    public string? NameAssignTo { get; set; } = default!;
    public string? EmailAssignTo { get; set; } = default!;
    public string? ProffofExecution { get; set; } = default!;
    public string? Client { get; set; } = default!;
    public Guid? DocumentId { get; set; } = default!;
    public Document Document { get; set; } = default!;
    public RequestType RequestType { get; set; } = default!;
    public RequestStatus Status { get; set; } = default!;
    public RequestStatus? StatusSap { get; set; } = default!;
    public ICollection<GeneralLedgerLineDetail> GeneralLedgerLineDetails { get; set; } = default!;
    public ICollection<StatusHistory> StatusHistorys { get; set; } = default!;
    public ICollection<GeneralLedgerFile> GeneralLedgerFiles { get; set; } = default!;
}
