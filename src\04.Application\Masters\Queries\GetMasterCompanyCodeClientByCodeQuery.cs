﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Masters;
using Pertamina.WebFIPosting.Shared.Masters.MasterCompanyCodeClient.Constants;
using Pertamina.WebFIPosting.Shared.Masters.MasterCompanyCodeClient.Queries;

namespace Pertamina.WebFIPosting.Application.Masters.Queries;
public class GetMasterCompanyCodeClientByCodeQuery : IRequest<GetMasterCompanyCodeClientByCodeResponse>
{
    public string CoCode { get; set; } = default!;
}

public class GetMasterCompanyCodeClientByCodeQueryMapping : IMapFrom<MasterCompanyCodeClient, GetMasterCompanyCodeClientByCodeResponse>
{
}
public class GetMasterCompanyCodeClientByCodeQueryHandler : IRequestHandler<GetMasterCompanyCodeClientByCodeQuery, GetMasterCompanyCodeClientByCodeResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;

    public GetMasterCompanyCodeClientByCodeQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<GetMasterCompanyCodeClientByCodeResponse> Handle(GetMasterCompanyCodeClientByCodeQuery request, CancellationToken cancellationToken)
    {
        var query = await _context.MasterCompanyCodeClient
            .Where(x => x.CoCode == request.CoCode).SingleOrDefaultAsync(cancellationToken);

        if (query is null)
        {
            throw new NotFoundException(DisplayTextFor.MasterCompanyCodeClient, request.CoCode);
        }

        return _mapper.Map<GetMasterCompanyCodeClientByCodeResponse>(query);
    }
}
