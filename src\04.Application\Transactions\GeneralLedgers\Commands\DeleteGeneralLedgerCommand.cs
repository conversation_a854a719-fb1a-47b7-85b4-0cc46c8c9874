﻿using MediatR;
using Pertamina.WebFIPosting.Application.Common.Attributes;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Shared.Services.Authorization.Constants;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgers.Commands;

namespace Pertamina.WebFIPosting.Application.Transactions.GeneralLedgers.Commands;
[Authorize(Policy = Permissions.WebFi_R2R_Requetor_Crud)]
public class DeleteGeneralLedgerCommand : IRequest<GeneralLedgerResponse>
{
    public Guid Id { get; set; }
}

public class DeleteGeneralLedgerCommandHandler : IRequestHandler<DeleteGeneralLedgerCommand, GeneralLedgerResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    public DeleteGeneralLedgerCommandHandler(IWebFiPlatformDbContext context)
    {
        _context = context;
    }
    public async Task<GeneralLedgerResponse> Handle(DeleteGeneralLedgerCommand request, CancellationToken cancellationToken)
    {
        var generalLedger = await _context.GeneralLedger.FindAsync(request.Id);
        if (generalLedger == null)
        {
            throw new NotFoundException("General Ledger not found.");
        }

        generalLedger.IsDeleted = true;
        await _context.SaveChangesAsync(cancellationToken);
        return new GeneralLedgerResponse() { Id = generalLedger.Id };
    }
}
