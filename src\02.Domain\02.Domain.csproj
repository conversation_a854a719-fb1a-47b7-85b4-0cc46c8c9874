﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>Pertamina.WebFIPosting.Domain</AssemblyName>
    <RootNamespace>Pertamina.WebFIPosting.Domain</RootNamespace>
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CS1591</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <CompilerVisibleProperty Include="RootNamespace" />
    <CompilerVisibleProperty Include="ProjectDir" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\01.Base\01.Base.csproj" />
  </ItemGroup>
</Project>
