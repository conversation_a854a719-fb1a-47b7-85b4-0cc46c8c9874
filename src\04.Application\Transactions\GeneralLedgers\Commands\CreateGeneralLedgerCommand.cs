﻿//using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Application.Services.Sap;
using Pertamina.WebFIPosting.Application.Services.Storage;
using Pertamina.WebFIPosting.Domain.Entities.Transactions;
using Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgers.Commands;

namespace Pertamina.WebFIPosting.Application.Transactions.GeneralLedgers.Commands;

public class CreateGeneralLedgerCommand : GeneralLedgerRequest, IRequest<GeneralLedgerResponse>
{
}

//public class CreateGeneralLedgerCommandValidator : AbstractValidator<CreateGeneralLedgerCommand>
//{
//    public CreateGeneralLedgerCommandValidator()
//    {
//        Include(new GeneralLedgerRequestValidator());
//    }
//}

public class CreateGeneralLedgerCommandHandler : IRequestHandler<CreateGeneralLedgerCommand, GeneralLedgerResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IStorageService _storageService;
    private readonly ISapService _sapService;
    public CreateGeneralLedgerCommandHandler(IWebFiPlatformDbContext context, IStorageService storageService, ISapService sapService)
    {
        _context = context;
        _storageService = storageService;
        _sapService = sapService;
    }
    public async Task<GeneralLedgerResponse> Handle(CreateGeneralLedgerCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var format = "GL" + DateTime.Now.Year + DateTime.Now.Month.ToString().PadLeft(2, '0') + DateTime.Now.Day.ToString().PadLeft(2, '0');
            var firstNumber = 1;
            var seqNumber = _context.GeneralLedger.Count() == 0 ? firstNumber.ToString().PadLeft(5, '0') : (_context.GeneralLedger.Where(x => x.TicketNo.StartsWith(format)).Count() + 1).ToString().PadLeft(5, '0');
            var noTicket = format + seqNumber;

            var valueMax = request.GeneralLedgerLineDetails.Select(x => x.AmountinDoc).Max();

            var dataApprover = await _context.MasterValidationApprovalRR
            .Where(x => x.CompanyCode == request.CompanyCodeTransactionId)
            .Where(x => x.Currency == request.Currency)
            .Where(x => valueMax >= x.NilaiAwal && valueMax <= x.NilaiAkhir).FirstOrDefaultAsync();

            var setData = new GeneralLedger
            {
                TicketNo = noTicket,
                Approver1 = dataApprover.Approver1,
                NameApprover1 = dataApprover.ApproverName1,
                EmailApprover1 = dataApprover.ApproverLoginID1,
                Approver2 = dataApprover.Approver2,
                NameApprover2 = dataApprover.ApproverName2,
                EmailApprover2 = dataApprover.ApproverLoginID2,
                Approver3 = dataApprover.Approver3,
                NameApprover3 = dataApprover.ApproverName3,
                EmailApprover3 = dataApprover.ApproverLoginID3,
                TargetDate = DateOnly.FromDateTime(DateTime.Now),
                ApproverPjsVacan = request.ApproverPjsVacan,
                CompanyCodeRequestorId = request.CompanyCodeRequestorId,
                CostCenterRequestor = request.CostCenterRequestor,
                EmailRequestor = request.EmailRequestor,
                JobTitleRequestor = request.JobTitleRequestor,
                NameRequestor = request.NameRequestor,
                PhoneNumberRequestor = request.PhoneNumberRequestor,
                Metode = request.Metode,
                CompanyCodeTransactionId = request.CompanyCodeTransactionId,
                Currency = request.Currency,
                Client = request.Client,
                CurrentPeriod = request.CurrentPeriod,
                RequestType = Base.Enums.RequestType.GLPosting,
                CategoryRequest = request.CategoryRequest,
                Status = (Base.Enums.RequestStatus)request.Status,
            };

            await _context.GeneralLedger.AddAsync(setData, cancellationToken);

            var responseSimulate = await _sapService.SimulateGLPostingsync(request.GeneralLedgerLineDetails, request, _context);

            var historyStatus = new StatusHistory()
            {
                Status = (Base.Enums.RequestStatus)request.Status,
                StatusType = "RR",
                GeneralLedgerId = setData.Id,
                TotalApproval = setData.Approver1 != null && setData.Approver2 != null ? 2 : setData.Approver1 != null && setData.Approver2 != null && setData.Approver3 != null ? 3 : 1,
                Notes = request.Notes
            };

            await _context.StatusHistory.AddAsync(historyStatus, cancellationToken);

            var historyStatusComment = new StatusComment()
            {
                StatusHistoryId = historyStatus.Id,
                Comment = request.Notes
            };

            await _context.StatusComment.AddAsync(historyStatusComment, cancellationToken);

            if (request.Files != null && request.Files.Any())
            {
                foreach (var file in request.Files)
                {
                    using var memoryStream = new MemoryStream();
                    await file.CopyToAsync(memoryStream, cancellationToken);
                    memoryStream.Position = 0;
                    var fileData = memoryStream.ToArray();

                    var document = new GeneralLedgerFile
                    {
                        GeneralLedgerId = setData.Id,
                        FileName = file.FileName,
                        FileSize = file.Length,
                        FileContentType = file.ContentType,
                        StorageFileId = await _storageService.CreateAsync(fileData)
                    };

                    await _context.GeneralLedgerFile.AddAsync(document, cancellationToken);
                }
            }

            if (request.GeneralLedgerLineDetails != null && request.GeneralLedgerLineDetails.Any())
            {
                foreach (var lineDetail in request.GeneralLedgerLineDetails)
                {
                    var lineDetailData = new GeneralLedgerLineDetail
                    {
                        AmountinDoc = lineDetail.AmountinDoc,
                        AmountinLC = lineDetail.AmountinLC,
                        AmountinLC2 = lineDetail.AmountinLC2,
                        AmountinLC3 = lineDetail.AmountinLC3,
                        Assignment = lineDetail.Assignment,
                        BussinessPlace = lineDetail.BussinessPlace,
                        CompanyCode = lineDetail.CompanyCode,
                        CostCenter = lineDetail.CostCenter,
                        DebitCredit = lineDetail.DebitCredit,
                        DocHeaderText = lineDetail.DocHeaderText,
                        DocType = lineDetail.DocType,
                        DocumentDate = lineDetail.DocumentDate,
                        GLAccount = lineDetail.GLAccount,
                        GeneralLedgerId = setData.Id,
                        InternalOrder = lineDetail.InternalOrder,
                        IsSimulate = lineDetail.IsSimulate,
                        Item = lineDetail.Item,
                        Plant = lineDetail.Plant,
                        PostingDate = lineDetail.PostingDate,
                        Period = lineDetail.Period,
                        ProfitCenter = lineDetail.ProfitCenter,
                        RecIndicator = lineDetail.RecIndicator,
                        Reference = lineDetail.Reference,
                        ReferenceKey1 = lineDetail.ReferenceKey1,
                        ReferenceKey2 = lineDetail.ReferenceKey2,
                        ReferenceKey3 = lineDetail.ReferenceKey3,
                        TradingPartner = lineDetail.TradingPartner,
                        TransactnType = lineDetail.TransactnType,
                        Text = lineDetail.Text,
                        TextCode = lineDetail.TextCode,
                        TranslationDate = lineDetail.TranslationDate,
                        Wbs = lineDetail.Wbs,
                        Currency = lineDetail.Currency,
                        Status = lineDetail.Status,
                        Year = !string.IsNullOrEmpty(lineDetail.Year) ? int.Parse(lineDetail.Year) : null,
                        PostedAmmount = lineDetail.PostedAmmount,
                        DocNumberPosted = lineDetail.DocNumberPosted,
                        DocCurrency = lineDetail.DocCurrency,
                        Message = lineDetail.Message,
                        SimulatedResponseCode = lineDetail.SimulatedResponseCode,
                        SimulatedResponseMessage = lineDetail.SimulatedResponseMessage,
                        SimulateDate = lineDetail.SimulateDate

                    };
                    await _context.GeneralLedgerLineDetail.AddAsync(lineDetailData, cancellationToken);
                }
            }

            await _context.SaveChangesAsync(cancellationToken);

            return new GeneralLedgerResponse() { Id = setData.Id };
        }
        catch (Exception ex)
        {
            throw new NotImplementedException("Error while creating General Ledger", ex);
        }
    }
}
