﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>Pertamina.WebFIPosting.Shared</AssemblyName>
    <RootNamespace>Pertamina.WebFIPosting.Shared</RootNamespace>
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CS1591</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <CompilerVisibleProperty Include="RootNamespace" />
    <CompilerVisibleProperty Include="ProjectDir" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.2" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="8.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="5.0.17" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\01.Base\01.Base.csproj" />
    <ProjectReference Include="..\02.Domain\02.Domain.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Bmcs\Queries\" />
    <Folder Include="Masters\MasterCompanyCodeClient\Commands\" />
    <Folder Include="Masters\MasterCompanyCodeCurency\Commands\" />
    <Folder Include="Masters\ZTBCOCDCURR\Commands\" />
    <Folder Include="Transactions\Statuses\Queries\" />
  </ItemGroup>
</Project>
