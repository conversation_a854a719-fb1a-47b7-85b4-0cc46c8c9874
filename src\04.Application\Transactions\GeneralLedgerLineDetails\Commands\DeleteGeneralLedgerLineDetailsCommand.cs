﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgerLineDetails.Commands;

namespace Pertamina.WebFIPosting.Application.Transactions.GeneralLedgerLineDetails.Commands;

public class DeleteGeneralLedgerLineDetailsCommand : IRequest<GeneralLedgerLineDetailResponse>
{
    public Guid Id { get; set; }
}

public class DeleteGeneralLedgerLineDetailsCommandValidator : AbstractValidator<DeleteGeneralLedgerLineDetailsCommand>
{
    public DeleteGeneralLedgerLineDetailsCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Id is Required.");
    }
}

public class DeleteGeneralLedgerLineDetailsCommandHandler : IRequestHandler<DeleteGeneralLedgerLineDetailsCommand, GeneralLedgerLineDetailResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    public DeleteGeneralLedgerLineDetailsCommandHandler(IWebFiPlatformDbContext context)
    {
        _context = context;
    }

    public async Task<GeneralLedgerLineDetailResponse> Handle(DeleteGeneralLedgerLineDetailsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var data = await _context.GeneralLedgerLineDetail
                .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
            if (data is null)
            {
                throw new NotFoundException($"General Ledger Line Detail with ID {request.Id} not found.");
            }

            _context.GeneralLedgerLineDetail.Remove(data);
            await _context.SaveChangesAsync(cancellationToken);

            return new GeneralLedgerLineDetailResponse { Id = request.Id };
        }
        catch (Exception ex)
        {
            throw new NotImplementedException("Error while deleting Reverse Line Detail", ex);
        }
    }
}
