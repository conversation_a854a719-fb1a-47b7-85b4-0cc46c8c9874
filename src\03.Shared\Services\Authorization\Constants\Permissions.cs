﻿namespace Pertamina.WebFIPosting.Shared.Services.Authorization.Constants;

public static class Permissions
{
    public const string WebFi_Api_Audience = "webfi.api.audience";
    public const string WebFi_HealthCheck_View = "webfi.healthcheck.view";

    #region Master

    #endregion

    #region Essential Permissions
    public const string WebFi_R2R_Requetor_View = "webfi.r2r.request.view";
    public const string WebFi_R2R_Requetor_Crud = "webfi.r2r.request.crud";
    public const string WebFi_R2R_Requetor_Approver = "webfi.r2r.approver";
    public const string WebFi_R2R_Requetor_Fulfiller = "webfi.r2r.fulfiller";
    #endregion Essential Permissions

    public static readonly string[] All = new string[]
    {
        WebFi_Api_Audience,
        WebFi_HealthCheck_View,

        #region Essential Permissions
        WebFi_R2R_Requetor_View,
        WebFi_R2R_Requetor_Crud,
        WebFi_R2R_Requetor_Approver,
        #endregion Essential Permissions

    };

    public static readonly string[] Requestor = new string[]
    {
        WebFi_R2R_Requetor_View,
        WebFi_R2R_Requetor_Crud,
    };

    public static readonly string[] Fulfiller = new string[]
    {
        WebFi_R2R_Requetor_Fulfiller
    };

    public static readonly string[] Approver = new string[]
    {
        WebFi_R2R_Requetor_Approver
    };
}
