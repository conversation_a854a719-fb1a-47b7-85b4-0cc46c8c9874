﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Masters;
using Pertamina.WebFIPosting.Shared.Masters.MasterCompanyCodeCurency.Constants;
using Pertamina.WebFIPosting.Shared.Masters.MasterCompanyCodeCurency.Queries;

namespace Pertamina.WebFIPosting.Application.Masters.Queries;
public class GetMasterCompanyCodeCurencyByCodeQuery : IRequest<GetMasterCompanyCodeCurencyByCodeResponse>
{
    public string CoCode { get; set; } = default!;
}

public class GetMasterCompanyCodeCurencyByCodeQueryMapping : IMapFrom<MasterCompanyCodeCurency, GetMasterCompanyCodeCurencyByCodeResponse>
{
}

public class GetMasterCompanyCodeCurencyByCodeQueryHandler : IRequestHandler<GetMasterCompanyCodeCurencyByCodeQuery, GetMasterCompanyCodeCurencyByCodeResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;

    public GetMasterCompanyCodeCurencyByCodeQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<GetMasterCompanyCodeCurencyByCodeResponse> Handle(GetMasterCompanyCodeCurencyByCodeQuery request, CancellationToken cancellationToken)
    {
        var query = await _context.MasterCompanyCodeCurency
            .Where(x => x.CoCode == request.CoCode).SingleOrDefaultAsync(cancellationToken);

        if (query is null)
        {
            throw new NotFoundException(DisplayTextFor.MasterCompanyCodeCurency, request.CoCode);
        }

        return _mapper.Map<GetMasterCompanyCodeCurencyByCodeResponse>(query);
    }
}
