﻿using Pertamina.WebFIPosting.Domain.Abstracts;
using Pertamina.WebFIPosting.Domain.Events;

namespace Pertamina.WebFIPosting.Domain.Entities;

public class Document : FileEntity, IHasDomainEvent
{
    public string Title { get; set; } = default!;
    public string CompanyCode { get; set; } = default!;
    public string DocumentCategoryName { get; set; } = default!;
    public int JrdpYear { get; set; }
    public string TopicCode { get; set; } = default!;
    public string? EcmContentId { get; set; }

    public IList<DomainEvent> DomainEvents { get; set; } = new List<DomainEvent>();
}
