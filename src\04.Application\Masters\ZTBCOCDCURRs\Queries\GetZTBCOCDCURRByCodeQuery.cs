﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Masters;
using Pertamina.WebFIPosting.Shared.Masters.ZTBCOCDCURR.Constants;
using Pertamina.WebFIPosting.Shared.Masters.ZTBCOCDCURR.Queries;

namespace Pertamina.WebFIPosting.Application.Masters.ZTBCOCDCURRs.Queries;
public class GetZTBCOCDCURRByCodeQuery : IRequest<GetZTBCOCDCURRByCodeResponse>
{
    public string CoCode { get; set; } = default!;
}

public class GetZTBCOCDCURRByQueryMapping : IMapFrom<ZTBCOCDCURR, GetZTBCOCDCURRByCodeResponse>
{
}

public class GetZTBCOCDCURRByCodeQueryHandler : IRequestHandler<GetZTBCOCDCURRByCodeQuery, GetZTBCOCDCURRByCodeResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;

    public GetZTBCOCDCURRByCodeQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<GetZTBCOCDCURRByCodeResponse> Handle(GetZTBCOCDCURRByCodeQuery request, CancellationToken cancellationToken)
    {
        var query = await _context.ZTBCOCDCURR
            .Where(x => x.COCODE == request.CoCode).FirstOrDefaultAsync(cancellationToken);

        if (query is null)
        {
            throw new NotFoundException(DisplayTextFor.ZTBCOCDCURR, request.CoCode);
        }

        return _mapper.Map<GetZTBCOCDCURRByCodeResponse>(query);
    }
}
