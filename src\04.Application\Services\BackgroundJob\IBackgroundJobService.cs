﻿using System.Linq.Expressions;

namespace Pertamina.WebFIPosting.Application.Services.BackgroundJob;

public interface IBackgroundJobService
{
    public Task<string> RunJob(Expression<Func<Task>> methodCall);

    public Task<string> AddScheduledJob(Expression<Func<Task>> methodCall, DateTimeOffset enqueueAt);
    public Task<bool> RemoveScheduledJob(string scheduledJobId);

    public Task<bool> AddContinuationJob(string parentJobId, Expression<Func<Task>> methodCall);

    public Task<bool> AddRecurringJob(string recurringJobId, Expression<Func<Task>> methodCall, string cronExpression, TimeZoneInfo timeZone = default!);
    public Task<bool> UpdateRecurringJob(string recurringJobId, Expression<Func<Task>> methodCall, string cronExpression, TimeZoneInfo timeZone = default!);
    public Task<bool> RemoveRecurringJob(string recurringJobId);
}
