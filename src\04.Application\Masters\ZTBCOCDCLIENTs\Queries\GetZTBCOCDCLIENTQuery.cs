﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Extensions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Masters;
using Pertamina.WebFIPosting.Shared.Common.Responses;
using Pertamina.WebFIPosting.Shared.Masters.ZTBCOCDCLIENTs.Queries;

namespace Pertamina.WebFIPosting.Application.Masters.ZTBCOCDCLIENTs.Queries;

public class GetZTBCOCDCLIENTQuery : GetZTBCOCDCLIENTRequest, IRequest<ListResponse<GetZTBCOCDCLIENTResponse>>
{
}
public class GetZTBCOCDCLIENTQueryMapper : IMapFrom<ZTBCOCDCLIENT, GetZTBCOCDCLIENTResponse> { }

public class GetZTBCOCDCLIENTQueryHandler : IRequestHandler<GetZTBCOCDCLIENTQuery, ListResponse<GetZTBCOCDCLIENTResponse>>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;
    public GetZTBCOCDCLIENTQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<ListResponse<GetZTBCOCDCLIENTResponse>> Handle(GetZTBCOCDCLIENTQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var query = await _context.ZTBCOCDCLIENT
                .AsNoTracking()
                .Where(x => x.ISDELETED == "0")
                .Where(x => string.IsNullOrEmpty(request.COCODE) ? true : x.COCODE == request.COCODE)
                .ProjectTo<GetZTBCOCDCLIENTResponse>(_mapper.ConfigurationProvider)
                .ToListAsync(cancellationToken);

            return query.ToListResponse();
        }
        catch (Exception ex)
        {
            throw new NotImplementedException($"{ex.Message}");
        }
    }
}
