﻿using Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;
using Pertamina.WebFIPosting.Shared.Common.Responses;

namespace Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgerLineDetails.Queries;
public class GetGeneralLedgerLineDetailResponse : Response
{
    public Guid? Id { get; set; }
    public Guid GeneralLedgerId { get; set; }
    public string? Item { get; set; }
    public string? DebitCredit { get; set; }
    public string? CompanyCode { get; set; } = default!;
    public DateOnly? DocumentDate { get; set; }
    public DateOnly? PostingDate { get; set; }
    public int? Period { get; set; }
    public DateOnly? TranslationDate { get; set; }
    public string? GLAccount { get; set; }
    public string? Reference { get; set; }
    public string? DocHeaderText { get; set; }
    public string? DocType { get; set; }
    public string? Currency { get; set; } = default!;
    public decimal? AmountinDoc { get; set; }
    public decimal? AmountinLC { get; set; }
    public decimal? AmountinLC2 { get; set; }
    public decimal? AmountinLC3 { get; set; }
    public string? Text { get; set; }
    public string? TextCode { get; set; }
    public string? Assignment { get; set; }
    public string? CostCenter { get; set; }
    public string? Plant { get; set; } = default!;
    public string? ProfitCenter { get; set; }
    public string? InternalOrder { get; set; }
    public string? Wbs { get; set; }
    public string? BussinessPlace { get; set; }
    public string? RecIndicator { get; set; }
    public string? ReferenceKey1 { get; set; }
    public string? ReferenceKey2 { get; set; }
    public string? ReferenceKey3 { get; set; }
    public string? TradingPartner { get; set; }
    public string? TransactnType { get; set; }
    public string? CFTrader { get; set; }
    public string? CFActivity { get; set; }
    public string? NoDocumentClearing { get; set; }
    public string? YearDocumentClearing { get; set; }
    public string? AmountDocumentClearing { get; set; }
    public string? DocNumberPosted { get; set; }
    public string? DocCurrency { get; set; }
    public int? Year { get; set; }
    public string? PostedAmmount { get; set; }
    public string? Message { get; set; }
    public string? Status { get; set; }
    public string? SimulatedResponseCode { get; set; }
    public string? SimulatedResponseMessage { get; set; }
    public bool IsSimulate { get; set; }
    public DateTime? SimulateDate { get; set; }
    public GeneralLedger GeneralLedger { get; set; } = default!;
}
