﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Extensions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;
using Pertamina.WebFIPosting.Shared.Common.Extensions;
using Pertamina.WebFIPosting.Shared.Common.Responses;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgers.Queries;

namespace Pertamina.WebFIPosting.Application.Transactions.GeneralLedgers.Queries;
//[Authorize(Policy = Permissions.WebFi_R2R_Requetor_View)]
public class GetGeneralLedgersQuery : GetGeneralLedgerRequest, IRequest<ListResponse<GetGeneralLedgerReponse>>
{
}

public class GetGeneralLedgersQueryMapper : IMapFrom<GeneralLedger, GetGeneralLedgerReponse>
{
    public void Mapping(Profile profile)
    {
        if (profile is not null)
        {
            profile.CreateMap<GeneralLedger, GetGeneralLedgerReponse>()
                .ForMember(x => x.StatusName, o => o.MapFrom(s => s.Status.GetDescription()));
        }
    }
}

public class GetGeneralLedgersQueryHandler : IRequestHandler<GetGeneralLedgersQuery, ListResponse<GetGeneralLedgerReponse>>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;
    public GetGeneralLedgersQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }
    public async Task<ListResponse<GetGeneralLedgerReponse>> Handle(GetGeneralLedgersQuery request, CancellationToken cancellationToken)
    {
        var requestStatus = request.Status!.Split(',');
        var query = await _context.GeneralLedger
            .AsNoTracking()
            .Where(x => !x.IsDeleted)
            .Where(x => x.RequestType == Base.Enums.RequestType.GLPosting)
            .ProjectTo<GetGeneralLedgerReponse>(_mapper.ConfigurationProvider)
            .ToListAsync();

        query = query.Where(x => requestStatus.Contains(x.StatusName)).ToList();

        return query.ToListResponse();

    }
}
