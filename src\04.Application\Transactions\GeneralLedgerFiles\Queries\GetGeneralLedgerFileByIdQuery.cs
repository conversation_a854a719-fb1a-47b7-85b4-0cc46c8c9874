﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgerFiles.Queries;

namespace Pertamina.WebFIPosting.Application.Transactions.GeneralLedgerFiles.Queries;
public class GetGeneralLedgerFileByIdQuery : IRequest<GetGeneralLedgerFileResponse>
{
    public Guid Id { get; set; }
}

public class GetGeneralLedgerFileByIdQueryMapper : IMapFrom<GeneralLedger, GetGeneralLedgerFileResponse>
{
}

public class GetGeneralLedgerFileByIdQueryHandler : IRequestHandler<GetGeneralLedgerFileByIdQuery, GetGeneralLedgerFileResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;
    public GetGeneralLedgerFileByIdQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<GetGeneralLedgerFileResponse> Handle(GetGeneralLedgerFileByIdQuery request, CancellationToken cancellationToken)
    {
        var query = await _context.GeneralLedgerFile
            .AsNoTracking()
            .Where(x => !x.IsDeleted && x.Id == request.Id)
            .ProjectTo<GetGeneralLedgerFileResponse>(_mapper.ConfigurationProvider)
            .FirstOrDefaultAsync(cancellationToken);

        return query!;
    }
}
