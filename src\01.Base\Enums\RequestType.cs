﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel;

namespace Pertamina.WebFIPosting.Base.Enums;

public enum RequestType
{
    [Description("GL Posting")]
    [Display(Name = "GL Posting")]
    GLPosting = 0,

    [Description("Reverse")]
    [Display(Name = "Reverse")]
    Reverse = 1,

    [Description("Reset & Reverse Document Clearing")]
    [Display(Name = "Reset & Reverse Document Clearing")]
    ResetReverseDocumentClearing = 2,

    [Description("GL Posting with Clearing")]
    [Display(Name = "GL Posting with Clearing")]
    GLPostingwithClearing = 3,

    [Description("Recurring")]
    [Display(Name = "Recurring")]
    Recurring = 4,

    [Description("Manual Bank Statement")]
    [Display(Name = "Manual Bank Statement")]
    ManualBankStatement = 5,

    [Description("Manual Reposting")]
    [Display(Name = "Manual Reposting")]
    ManualReposting = 6,

    [Description("SKF Calculation")]
    [Display(Name = "SKF Calculation")]
    SKFCalculation = 7,

    [Description("Manual Allocation")]
    [Display(Name = "Manual Allocation")]
    ManualAllocation = 8,

    [Description("Billing FI (FB70)")]
    [Display(Name = "Billing FI (FB70)")]
    BillingFI = 9,

    [Description("Manual Billing & Unbilled Customer Non ICT (F-04)")]
    [Display(Name = "Manual Billing & Unbilled Customer Non ICT (F-04)")]
    ManualBillingNonICT = 10,

    [Description("Manual Billing & Unbilled Customer ICT (F-04)")]
    [Display(Name = "Manual Billing & Unbilled Customer ICT (F-04)")]
    ManualBillingCustomerICT = 11,
}
