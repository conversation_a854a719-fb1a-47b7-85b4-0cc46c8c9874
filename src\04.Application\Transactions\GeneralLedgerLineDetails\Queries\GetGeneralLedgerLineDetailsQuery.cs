﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Extensions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;
using Pertamina.WebFIPosting.Shared.Common.Responses;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgerLineDetails.Queries;

namespace Pertamina.WebFIPosting.Application.Transactions.GeneralLedgerLineDetails.Queries;
public class GetGeneralLedgerLineDetailsQuery : GetGeneralLedgerLineDetailRequest, IRequest<ListResponse<GetGeneralLedgerLineDetailResponse>>
{
}

public class GetGeneralLedgerLineDetailsQueryMapper : IMapFrom<GeneralLedgerLineDetail, GetGeneralLedgerLineDetailResponse>
{
    public void Mapping(Profile profile)
    {
        profile?.CreateMap<GeneralLedgerLineDetail, GetGeneralLedgerLineDetailResponse>();
    }
}
public class GetGeneralLedgerLineDetailsQueryHandler : IRequestHandler<GetGeneralLedgerLineDetailsQuery, ListResponse<GetGeneralLedgerLineDetailResponse>>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;
    public GetGeneralLedgerLineDetailsQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<ListResponse<GetGeneralLedgerLineDetailResponse>> Handle(GetGeneralLedgerLineDetailsQuery request, CancellationToken cancellationToken)
    {
        var query = await _context.GeneralLedgerLineDetail
            .Include(x => x.GeneralLedger)
            .AsNoTracking()
            .Where(x => !x.IsDeleted)
            .ProjectTo<GetGeneralLedgerLineDetailResponse>(_mapper.ConfigurationProvider)
            .ToListAsync(cancellationToken);

        return query.ToListResponse();
    }
}
