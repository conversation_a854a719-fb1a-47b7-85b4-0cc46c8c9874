﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Extensions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Masters;
using Pertamina.WebFIPosting.Shared.Common.Responses;
using Pertamina.WebFIPosting.Shared.Masters.MasterCompanyCodeClient.Queries;

namespace Pertamina.WebFIPosting.Application.Masters.Queries;
public class GetMasterCompanyCodeClientQuery : IRequest<ListResponse<GetMasterCompanyCodeClientQueryResponse>>
{
}

public class GetMasterCompanyCodeClientQueryMapping : IMapFrom<MasterCompanyCodeClient, GetMasterCompanyCodeClientQueryResponse>
{
    public void Mapping(Profile profile)
    {
        if (profile is not null)
        {
            profile.CreateMap<MasterCompanyCodeClient, GetMasterCompanyCodeClientQueryResponse>();
        }
    }
}

public class GetMasterCompanyCodeClientQueryHandler : IRequestHandler<GetMasterCompanyCodeClientQuery, ListResponse<GetMasterCompanyCodeClientQueryResponse>>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;

    public GetMasterCompanyCodeClientQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<ListResponse<GetMasterCompanyCodeClientQueryResponse>> Handle(GetMasterCompanyCodeClientQuery request, CancellationToken cancellationToken)
    {
        var query = await _context.MasterCompanyCodeClient
            .AsNoTracking()
            //.Where(x => !x.IsDeleted)
            .ProjectTo<GetMasterCompanyCodeClientQueryResponse>(_mapper.ConfigurationProvider)
            .ToListAsync(cancellationToken);

        return query.ToListResponse();
    }
}
