﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Attributes;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities;
using Pertamina.WebFIPosting.Shared.Audits.Constants;
using Pertamina.WebFIPosting.Shared.Audits.Queries.GetAudit;
using Pertamina.WebFIPosting.Shared.Services.Authorization.Constants;

namespace Pertamina.WebFIPosting.Application.Audits.Queries.GetAudit;

[Authorize(Policy = Permissions.WebFi_Api_Audience)]
public class GetAuditQuery : IRequest<GetAuditResponse>
{
    public Guid AuditId { get; set; }
}

public class GetAuditResponseMapping : IMapFrom<Audit, GetAuditResponse>
{
}

public class GetAuditQueryHandler : IRequestHandler<GetAuditQuery, GetAuditResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;

    public GetAuditQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<GetAuditResponse> Handle(GetAuditQuery request, CancellationToken cancellationToken)
    {
        var audit = await _context.Audits
            .AsNoTracking()
            .Where(x => x.Id == request.AuditId)
            .SingleOrDefaultAsync(cancellationToken);

        if (audit is null)
        {
            throw new NotFoundException(DisplayTextFor.Audit, request.AuditId);
        }

        return _mapper.Map<GetAuditResponse>(audit);
    }
}
