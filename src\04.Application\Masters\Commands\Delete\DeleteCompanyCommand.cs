﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Shared.Masters.Company.Constants;

namespace Pertamina.WebFIPosting.Application.Masters.Commands.Delete;
public class DeleteCompanyCommand : IRequest
{
    public string Code { get; set; } = default!;
}

public class DeleteCompanyCommandHandler : IRequestHandler<DeleteCompanyCommand>
{
    private readonly IWebFiPlatformDbContext _context;

    public DeleteCompanyCommandHandler(IWebFiPlatformDbContext context)
    {
        _context = context;
    }

    public async Task<Unit> Handle(DeleteCompanyCommand request, CancellationToken cancellationToken)
    {
        var query = await _context.Company.Where(x => x.Code == request.Code).FirstOrDefaultAsync(cancellationToken);

        if (query is null)
        {
            throw new NotFoundException(DisplayTextFor.Company, request.Code);
        }

        query.IsDeleted = true;
        await _context.SaveChangesAsync(this, cancellationToken);

        return Unit.Value;
    }
}
