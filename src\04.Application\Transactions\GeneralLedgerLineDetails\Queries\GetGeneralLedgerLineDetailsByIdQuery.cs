﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgerLineDetails.Queries;

namespace Pertamina.WebFIPosting.Application.Transactions.GeneralLedgerLineDetails.Queries;

public class GetGeneralLedgerLineDetailsByIdQuery : IRequest<GetGeneralLedgerLineDetailResponse>
{
    public Guid Id { get; set; }
}

public class GetGeneralLedgerLineDetailsByIdQueryMapper : IMapFrom<GeneralLedgerLineDetail, GetGeneralLedgerLineDetailResponse>
{
    public void Mapping(Profile profile)
    {
        profile?.CreateMap<GeneralLedgerLineDetail, GetGeneralLedgerLineDetailResponse>();
    }
}

public class GetGeneralLedgerLineDetailsByIdQueryHandler : IRequestHandler<GetGeneralLedgerLineDetailsByIdQuery, GetGeneralLedgerLineDetailResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;
    public GetGeneralLedgerLineDetailsByIdQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }
    public async Task<GetGeneralLedgerLineDetailResponse> Handle(GetGeneralLedgerLineDetailsByIdQuery request, CancellationToken cancellationToken)
    {
        var query = await _context.GeneralLedgerLineDetail
            .Include(x => x.GeneralLedger)
            .AsNoTracking()
            .Where(x => !x.IsDeleted && x.Id == request.Id)
            .ProjectTo<GetGeneralLedgerLineDetailResponse>(_mapper.ConfigurationProvider)
            .FirstOrDefaultAsync(cancellationToken);

        return query;
    }
}
