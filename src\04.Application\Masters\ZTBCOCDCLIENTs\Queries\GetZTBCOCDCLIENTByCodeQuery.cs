﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Masters;
using Pertamina.WebFIPosting.Shared.Masters.ZTBCOCDCLIENTs.Constants;
using Pertamina.WebFIPosting.Shared.Masters.ZTBCOCDCLIENTs.Queries;

namespace Pertamina.WebFIPosting.Application.Masters.ZTBCOCDCLIENTs.Queries;

public class GetZTBCOCDCLIENTByCodeQuery : IRequest<GetZTBCOCDCLIENTResponse>
{
    public string CoCode { get; set; } = default!;
}

public class GetZTBCOCDCLIENTByCodeQueryMapping : IMapFrom<ZTBCOCDCLIENT, GetZTBCOCDCLIENTResponse>
{
}

public class GetZTBCOCDCLIENTByCodeQueryHandler : IRequestHandler<GetZTBCOCDCLIENTByCodeQuery, GetZTBCOCDCLIENTResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;

    public GetZTBCOCDCLIENTByCodeQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<GetZTBCOCDCLIENTResponse> Handle(GetZTBCOCDCLIENTByCodeQuery request, CancellationToken cancellationToken)
    {
        var query = await _context.ZTBCOCDCLIENT
            .Where(x => x.COCODE == request.CoCode).FirstOrDefaultAsync(cancellationToken);
        if (query is null)
        {
            throw new NotFoundException(DisplayTextFor.ZTBCOCDCLIENT, request.CoCode);
        }

        return _mapper.Map<GetZTBCOCDCLIENTResponse>(query);
    }
}
