﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Application.Services.Sap;
using Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgers.Commands;

namespace Pertamina.WebFIPosting.Application.Transactions.GeneralLedgers.Commands;
//[Authorize(Policy = Permissions.WebFi_R2R_Requetor_Crud)]
public class UpdateGeneralLedgersCommand : GeneralLedgerRequest, IRequest<GeneralLedgerResponse>
{
}
public class UpdateGeneralLedgersCommandHandler : IRequestHandler<UpdateGeneralLedgersCommand, GeneralLedgerResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly ISapService _sapService;
    public UpdateGeneralLedgersCommandHandler(IWebFiPlatformDbContext context, ISapService sapService)
    {
        _context = context;
        _sapService = sapService;
    }

    public async Task<GeneralLedgerResponse> Handle(UpdateGeneralLedgersCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var valueMax = request.GeneralLedgerLineDetails.Select(x => x.AmountinDoc).Max();
            var dataApprover = await _context.MasterValidationApprovalRR
            .Where(x => x.CompanyCode == request.CompanyCodeTransactionId)
            .Where(x => x.Currency == request.Currency)
            .Where(x => valueMax >= x.NilaiAwal && valueMax <= x.NilaiAkhir).FirstOrDefaultAsync();

            var query = await _context.GeneralLedger.Where(x => x.Id == request.Id).FirstOrDefaultAsync(cancellationToken);
            if (query == null)
            {
                throw new NotFoundException("General Ledger File not found.");
            }

            query.Approver1 = dataApprover.Approver1;
            query.NameApprover1 = dataApprover.ApproverName1;
            query.EmailApprover1 = dataApprover.ApproverLoginID1;
            query.Approver2 = dataApprover.Approver2;
            query.NameApprover2 = dataApprover.ApproverName2;
            query.EmailApprover2 = dataApprover.ApproverLoginID2;
            query.Approver3 = dataApprover.Approver3;
            query.NameApprover3 = dataApprover.ApproverName3;
            query.EmailApprover3 = dataApprover.ApproverLoginID3;
            query.ApproverPjsVacan = request.ApproverPjsVacan;
            query.CompanyCodeRequestorId = request.CompanyCodeRequestorId;
            query.CostCenterRequestor = request.CostCenterRequestor;
            query.EmailRequestor = request.EmailRequestor;
            query.JobTitleRequestor = request.JobTitleRequestor;
            query.NameRequestor = request.NameRequestor;
            query.PhoneNumberRequestor = request.PhoneNumberRequestor;
            query.Metode = request.Metode;
            query.CompanyCodeTransactionId = request.CompanyCodeTransactionId;
            query.Currency = request.Currency;
            query.Client = request.Client;
            query.CurrentPeriod = request.CurrentPeriod;
            query.CategoryRequest = request.CategoryRequest;
            query.Status = (Base.Enums.RequestStatus)request.Status;

            var responseSimulate = await _sapService.SimulateGLPostingsync(request.GeneralLedgerLineDetails, request, _context);

            _context.GeneralLedgerLineDetail.RemoveRange(_context.GeneralLedgerLineDetail.Where(x => x.GeneralLedgerId == query.Id));

            if (request.GeneralLedgerLineDetails != null && request.GeneralLedgerLineDetails.Any())
            {
                foreach (var lineDetail in request.GeneralLedgerLineDetails)
                {
                    var lineDetailData = new GeneralLedgerLineDetail
                    {
                        AmountinDoc = lineDetail.AmountinDoc,
                        AmountinLC = lineDetail.AmountinLC,
                        AmountinLC2 = lineDetail.AmountinLC2,
                        AmountinLC3 = lineDetail.AmountinLC3,
                        Assignment = lineDetail.Assignment,
                        BussinessPlace = lineDetail.BussinessPlace,
                        CompanyCode = lineDetail.CompanyCode,
                        CostCenter = lineDetail.CostCenter,
                        DebitCredit = lineDetail.DebitCredit,
                        DocHeaderText = lineDetail.DocHeaderText,
                        DocType = lineDetail.DocType,
                        DocumentDate = lineDetail.DocumentDate,
                        GLAccount = lineDetail.GLAccount,
                        GeneralLedgerId = query.Id,
                        InternalOrder = lineDetail.InternalOrder,
                        IsSimulate = lineDetail.IsSimulate,
                        Item = lineDetail.Item,
                        Plant = lineDetail.Plant,
                        PostingDate = lineDetail.PostingDate,
                        Period = lineDetail.Period,
                        ProfitCenter = lineDetail.ProfitCenter,
                        RecIndicator = lineDetail.RecIndicator,
                        Reference = lineDetail.Reference,
                        ReferenceKey1 = lineDetail.ReferenceKey1,
                        ReferenceKey2 = lineDetail.ReferenceKey2,
                        ReferenceKey3 = lineDetail.ReferenceKey3,
                        TradingPartner = lineDetail.TradingPartner,
                        TransactnType = lineDetail.TransactnType,
                        Text = lineDetail.Text,
                        TextCode = lineDetail.TextCode,
                        TranslationDate = lineDetail.TranslationDate,
                        Wbs = lineDetail.Wbs,
                        Currency = lineDetail.Currency,
                        Status = lineDetail.Status,
                        Year = !string.IsNullOrEmpty(lineDetail.Year) ? int.Parse(lineDetail.Year) : null,
                        PostedAmmount = lineDetail.PostedAmmount,
                        DocNumberPosted = lineDetail.DocNumberPosted,
                        DocCurrency = lineDetail.DocCurrency,
                        Message = lineDetail.Message,
                        SimulatedResponseCode = lineDetail.SimulatedResponseCode,
                        SimulatedResponseMessage = lineDetail.SimulatedResponseMessage,
                        SimulateDate = lineDetail.SimulateDate

                    };
                    await _context.GeneralLedgerLineDetail.AddAsync(lineDetailData, cancellationToken);
                }
            }

            await _context.SaveChangesAsync(this, cancellationToken);

            return new GeneralLedgerResponse { Id = query.Id };
        }
        catch (Exception ex)
        {
            throw new NotImplementedException();
        }
    }
}
