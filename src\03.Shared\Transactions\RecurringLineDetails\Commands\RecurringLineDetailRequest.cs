﻿using FluentValidation;
using Pertamina.WebFIPosting.Shared.Common.Attributes;
using Pertamina.WebFIPosting.Shared.Common.Constants;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgers.Commands;

namespace Pertamina.WebFIPosting.Shared.Transactions.RecurringLineDetails.Commands;
public class RecurringLineDetailRequest
{
    [OpenApiContentType(ContentTypes.TextPlain)]
    public Guid? Id { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public Guid GeneralLedgerId { get; set; }

    [OpenApiContentType(ContentTypes.ApplicationJson)]
    public IEnumerable<GeneralLedgerRequest> GeneralLedger { get; set; } = default!;

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Item { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? CompanyCode { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateOnly? FirstRunDate { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateOnly? LastRunDate { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? IntervalInMonths { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateOnly? RunDate { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? DocumentType { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Reference { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Currency { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? DocheaderText { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? DebitCredit { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateOnly? DocumentDate { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateOnly? PostingDate { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public int? Period { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? GLAccount { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Reference1 { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? AmountInDoc { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? AmountInLC { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? AmountInLC2 { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? AmountInLC3 { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Text { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? TaxCode { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Assignment { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? CostCenter { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Plant { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? ProfitCenter { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? InternalOrder { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? WBS { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? BussinessPlace { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? RecIndicator { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? ReferenceKey1 { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? ReferenceKey2 { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? ReferenceKey3 { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? TradingPartner { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? TransactnType { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? DocNumberPosted { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? DocCurrency { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public int? Year { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? PostedAmmount { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Message { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Status { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? SimulatedResponseCode { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? SimulatedResponseMessage { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public bool IsSimulate { get; set; }

    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateTime? SimulateDate { get; set; }
}

public class RecurringLineDetailRequestValidator : AbstractValidator<RecurringLineDetailRequest>
{
    public RecurringLineDetailRequestValidator()
    {
        RuleFor(x => x.GeneralLedgerId)
            .NotEmpty()
            .WithMessage("General Ledger ID is required.");
    }
}
