﻿using Pertamina.WebFIPosting.Domain.Abstracts;

namespace Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;

public class RecurringLineDetail : AuditableEntity
{
    public Guid GeneralLedgerId { get; set; }
    public GeneralLedger GeneralLedger { get; set; } = default!;
    public string? Item { get; set; }
    public string? CompanyCode { get; set; }
    public DateOnly? FirstRunDate { get; set; }
    public DateOnly? LastRunDate { get; set; }
    public string? IntervalInMonths { get; set; }
    public DateOnly? RunDate { get; set; }
    public string? DocumentType { get; set; }
    public string? Reference { get; set; }
    public string? Currency { get; set; }
    public string? DocheaderText { get; set; }
    public string? DebitCredit { get; set; }
    public DateOnly? DocumentDate { get; set; }
    public DateOnly? PostingDate { get; set; }
    public int? Period { get; set; }
    public string? GLAccount { get; set; }
    public string? Reference1 { get; set; }
    public string? AmountInDoc { get; set; }
    public string? AmountInLC { get; set; }
    public string? AmountInLC2 { get; set; }
    public string? AmountInLC3 { get; set; }
    public string? Text { get; set; }
    public string? TaxCode { get; set; }
    public string? Assignment { get; set; }
    public string? CostCenter { get; set; }
    public string? Plant { get; set; }
    public string? ProfitCenter { get; set; }
    public string? InternalOrder { get; set; }
    public string? WBS { get; set; }
    public string? BussinessPlace { get; set; }
    public string? RecIndicator { get; set; }
    public string? ReferenceKey1 { get; set; }
    public string? ReferenceKey2 { get; set; }
    public string? ReferenceKey3 { get; set; }
    public string? TradingPartner { get; set; }
    public string? TransactnType { get; set; }
    public string? DocNumberPosted { get; set; }
    public string? DocCurrency { get; set; }
    public int? Year { get; set; }
    public string? PostedAmmount { get; set; }
    public string? Message { get; set; }
    public string? Status { get; set; }
    public string? SimulatedResponseCode { get; set; }
    public string? SimulatedResponseMessage { get; set; }
    public bool IsSimulate { get; set; }
    public DateTime? SimulateDate { get; set; }

}
