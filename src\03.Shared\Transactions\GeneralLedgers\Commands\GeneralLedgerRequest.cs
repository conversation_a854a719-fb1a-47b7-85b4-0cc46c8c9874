﻿using FluentValidation;
using Microsoft.AspNetCore.Http;
using Pertamina.WebFIPosting.Shared.Common.Attributes;
using Pertamina.WebFIPosting.Shared.Common.Constants;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgerLineDetails.Commands;

namespace Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgers.Commands;

public class GeneralLedgerRequest
{
    [OpenApiContentType(ContentTypes.TextPlain)]
    public Guid? Id { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string CompanyCodeRequestorId { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string CostCenterRequestor { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string JobTitleRequestor { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string NameRequestor { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string EmailRequestor { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string PhoneNumberRequestor { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string ApproverPjsVacan { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Approver1 { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? NameApprover1 { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? EmailApprover1 { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Approver2 { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? NameApprover2 { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? EmailApprover2 { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Approver3 { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? NameApprover3 { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? EmailApprover3 { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string Metode { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string CompanyCodeTransactionId { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string CategoryRequest { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string Currency { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string CurrentPeriod { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string Notes { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public int Status { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public Guid? DocumentId { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public int RequestType { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? SupportGroup { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? AssignTo { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? NameAssignTo { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? EmailAssignTo { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? ProffofExecution { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateTime? PostingDate { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateTime? DocumentDate { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateTime? TranslationDate { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? PostingPeriod { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? ClearingText { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Client { get; set; } = default!;

    [OpenApiContentType(ContentTypes.MultipartFormData)]
    public IEnumerable<IFormFile> Files { get; set; } = default!;
    [OpenApiContentType(ContentTypes.ApplicationJson)]
    public IEnumerable<GeneralLedgerLineDetailRequest> GeneralLedgerLineDetails { get; set; } = default!;
}

public class GeneralLedgerRequestValidator : AbstractValidator<GeneralLedgerRequest>
{
    public GeneralLedgerRequestValidator()
    {
        RuleFor(x => x.CompanyCodeRequestorId)
            .NotEmpty()
            .WithMessage("Company Code Requestor ID is required.");
        RuleFor(x => x.CostCenterRequestor)
            .NotEmpty()
            .WithMessage("Cost Center Requestor is required.");
        RuleFor(x => x.JobTitleRequestor)
            .NotEmpty()
            .WithMessage("Job Title Requestor is required.");
        RuleFor(x => x.NameRequestor)
            .NotEmpty()
            .WithMessage("Name Requestor is required.");
        RuleFor(x => x.EmailRequestor)
            .NotEmpty()
            .EmailAddress()
            .WithMessage("Valid Email Requestor is required.");
        RuleFor(x => x.PhoneNumberRequestor)
            .NotEmpty()
            .WithMessage("Phone Number Requestor is required.");
        RuleFor(x => x.Approver1)
            .NotEmpty()
            .EmailAddress()
            .WithMessage("Approver 1 is required.");
        RuleFor(x => x.Approver2)
            .NotEmpty()
            .EmailAddress()
            .WithMessage("Approver 2 is required.");
        RuleFor(x => x.Approver3)
            .NotEmpty()
            .EmailAddress()
            .WithMessage("Approver 3 is required.");
        RuleFor(x => x.Metode)
            .NotEmpty()
            .WithMessage("Metode is required.");
        RuleFor(x => x.CompanyCodeTransactionId)
            .NotEmpty()
            .WithMessage("Company Code Transaction ID is required.");
        RuleFor(x => x.Currency)
            .NotEmpty()
            .WithMessage("Currency is required.");
    }
}
