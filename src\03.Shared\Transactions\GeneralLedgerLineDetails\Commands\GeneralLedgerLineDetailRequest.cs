﻿using Pertamina.WebFIPosting.Shared.Common.Attributes;
using Pertamina.WebFIPosting.Shared.Common.Constants;

namespace Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgerLineDetails.Commands;

public class GeneralLedgerLineDetailRequest
{
    [OpenApiContentType(ContentTypes.TextPlain)]
    public Guid? Id { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public Guid GeneralLedgerId { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Item { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? DebitCredit { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? CompanyCode { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateOnly? DocumentDate { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateOnly? PostingDate { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public int? Period { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateOnly? TranslationDate { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? GLAccount { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Reference { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? DocHeaderText { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? DocType { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Currency { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public decimal? AmountinDoc { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public decimal? AmountinLC { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public decimal? AmountinLC2 { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public decimal? AmountinLC3 { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Text { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? TextCode { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Assignment { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? CostCenter { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Plant { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? ProfitCenter { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? InternalOrder { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Wbs { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? BussinessPlace { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? RecIndicator { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? ReferenceKey1 { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? ReferenceKey2 { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? ReferenceKey3 { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? ReferenceKey4 { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? TradingPartner { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? TransactnType { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? DocNumberPosted { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? DocCurrency { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Year { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? PostedAmmount { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Message { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public bool IsSimulate { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? Status { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateTime? SimulateDate { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? SimulatedResponseCode { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? SimulatedResponseMessage { get; set; }
}

