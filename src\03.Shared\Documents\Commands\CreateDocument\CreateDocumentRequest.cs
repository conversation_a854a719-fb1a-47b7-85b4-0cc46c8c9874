﻿using FluentValidation;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Pertamina.WebFIPosting.Shared.Common.Attributes;
using Pertamina.WebFIPosting.Shared.Common.Constants;
using Pertamina.WebFIPosting.Shared.Common.Extensions;
using Pertamina.WebFIPosting.Shared.Documents.Constants;
using Pertamina.WebFIPosting.Shared.Documents.Options;

namespace Pertamina.WebFIPosting.Shared.Documents.Commands.CreateDocument;

public class CreateDocumentRequest
{
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string Title { get; set; } = default!;

    [OpenApiContentType(ContentTypesFor.DocumentFile.Value)]
    public IFormFile File { get; set; } = default!;
}

public class CreateDocumentRequestValidator : AbstractValidator<CreateDocumentRequest>
{
    private readonly long _maximumFileSize;

    public CreateDocumentRequestValidator(IOptions<DocumentOptions> documentOptions)
    {
        _maximumFileSize = documentOptions.Value.MaximumFileSizeInBytes;

        RuleFor(v => v.Title)
            .NotEmpty()
            .MaximumLength(MaximumLengthFor.Title);

        RuleFor(v => v.File.Length)
            .ExclusiveBetween(0L, _maximumFileSize)
            .WithMessage($"{DisplayTextFor.Document} file size should be greater than 0 KB and less than {_maximumFileSize.ToReadableFileSize()}.");

        RuleFor(v => v.File.ContentType)
            .Must(HaveSupportedContentType)
            .WithMessage($"{DisplayTextFor.Document} file extension is not supported. Please {CommonDisplayTextFor.Upload.ToLower()} supported file.");
    }

    private bool HaveSupportedContentType(string contentType)
    {
        return ContentTypesFor.DocumentFile.List.Contains(contentType);
    }
}
