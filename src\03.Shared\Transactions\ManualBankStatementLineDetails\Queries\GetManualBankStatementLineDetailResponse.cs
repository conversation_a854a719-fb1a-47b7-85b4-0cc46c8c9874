﻿using Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;
using Pertamina.WebFIPosting.Shared.Common.Responses;

namespace Pertamina.WebFIPosting.Shared.Transactions.ManualBankStatementLineDetails.Queries;
public class GetManualBankStatementLineDetailResponse : Response
{
    public Guid? Id { get; set; }
    public Guid GeneralLedgerId { get; set; }
    public string? Item { get; set; }
    public string? DocHeaderText { get; set; }
    public string? TransactionType { get; set; }
    public string? TanggalText { get; set; }
    public string? Assignment { get; set; }
    public string? Currency { get; set; }
    public string? OpeningBalance { get; set; }
    public string? UangMasuk { get; set; }
    public string? UangKeluar { get; set; }
    public string? EndingBalance { get; set; }
    public string? DocNumberPosted { get; set; }
    public string? DocCurrency { get; set; }
    public int? Year { get; set; }
    public string? PostedAmmount { get; set; }
    public string? Message { get; set; }
    public string? Status { get; set; }
    public string? SimulatedResponseCode { get; set; }
    public string? SimulatedResponseMessage { get; set; }
    public bool IsSimulate { get; set; }
    public DateTime? SimulateDate { get; set; }
    public GeneralLedger GeneralLedger { get; set; } = default!;
}
