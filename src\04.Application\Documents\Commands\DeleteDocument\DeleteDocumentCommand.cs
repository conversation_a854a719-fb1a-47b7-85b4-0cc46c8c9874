﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Attributes;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Shared.Documents.Constants;
using Pertamina.WebFIPosting.Shared.Services.Authorization.Constants;

namespace Pertamina.WebFIPosting.Application.Documents.Commands.DeleteDocument;

[Authorize(Policy = Permissions.WebFi_Api_Audience)]
public class DeleteDocumentCommand : IRequest
{
    public Guid DocumentId { get; set; }
}

public class DeleteDocumentCommandHandler : IRequestHandler<DeleteDocumentCommand>
{
    private readonly IWebFiPlatformDbContext _context;

    public DeleteDocumentCommandHandler(IWebFiPlatformDbContext context)
    {
        _context = context;
    }

    public async Task<Unit> Handle(DeleteDocumentCommand request, CancellationToken cancellationToken)
    {
        var document = await _context.Documents
            .Where(x => !x.IsDeleted && x.Id == request.DocumentId)
            .SingleOrDefaultAsync(cancellationToken);

        if (document is null)
        {
            throw new NotFoundException(DisplayTextFor.Document, request.DocumentId);
        }

        document.IsDeleted = true;

        await _context.SaveChangesAsync(this, cancellationToken);

        return Unit.Value;
    }
}
