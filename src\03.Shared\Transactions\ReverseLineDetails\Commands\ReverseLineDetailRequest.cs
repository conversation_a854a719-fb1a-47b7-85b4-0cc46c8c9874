﻿using FluentValidation;
using Pertamina.WebFIPosting.Shared.Common.Attributes;
using Pertamina.WebFIPosting.Shared.Common.Constants;

namespace Pertamina.WebFIPosting.Shared.Transactions.ReverseLineDetails.Commands;
public class ReverseLineDetailRequest
{
    [OpenApiContentType(ContentTypes.TextPlain)]
    public Guid? Id { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public Guid GeneralLedgerId { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string Item { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string CompanyCode { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string DocumentNumber { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public int? FiscalYear { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string ReversalReason { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string Currency { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateOnly? PostingDate { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public decimal? AmountInDoc { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string DocNumberPosted { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string DocCurrency { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public int? Year { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string PostedAmmount { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string Message { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string Status { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string SimulatedResponseCode { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string SimulatedResponseMessage { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public bool IsSimulate { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateTime? SimulateDate { get; set; }
}

public class ReverseLineDetailRequestValidator : AbstractValidator<ReverseLineDetailRequest>
{
    public ReverseLineDetailRequestValidator()
    {
        RuleFor(x => x.GeneralLedgerId)
            .NotEmpty()
            .WithMessage("General Ledger ID is required.");
    }
}
