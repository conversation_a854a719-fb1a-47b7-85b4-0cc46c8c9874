﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Attributes;
using Pertamina.WebFIPosting.Application.Common.Extensions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities;
using Pertamina.WebFIPosting.Shared.Common.Enums;
using Pertamina.WebFIPosting.Shared.Common.Requests;
using Pertamina.WebFIPosting.Shared.Common.Responses;
using Pertamina.WebFIPosting.Shared.Documents.Queries.GetDocuments;
using Pertamina.WebFIPosting.Shared.Services.Authorization.Constants;

namespace Pertamina.WebFIPosting.Application.Documents.Queries.GetDocuments;

[Authorize(Policy = Permissions.WebFi_Api_Audience)]
public class GetDocumentsQuery : PaginatedListRequest, IRequest<PaginatedListResponse<GetDocumentsDocument>>
{
}

public class GetDocumentsQueryValidator : AbstractValidator<GetDocumentsQuery>
{
    public GetDocumentsQueryValidator()
    {
        Include(new PaginatedListRequestValidator());
    }
}

public class GetDocumentsDocumentMapping : IMapFrom<Document, GetDocumentsDocument>
{
}

public class GetDocumentsQueryHandler : IRequestHandler<GetDocumentsQuery, PaginatedListResponse<GetDocumentsDocument>>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;

    public GetDocumentsQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<PaginatedListResponse<GetDocumentsDocument>> Handle(GetDocumentsQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Documents
            .AsNoTracking()
            .Where(x => !x.IsDeleted)
            .ApplySearch(request.SearchText, typeof(GetDocumentsDocument), _mapper.ConfigurationProvider)
            .ApplyOrder(request.SortField, request.SortOrder,
                typeof(GetDocumentsDocument),
                _mapper.ConfigurationProvider,
                nameof(GetDocumentsDocument.Title),
                SortOrder.Asc);

        var result = await query
            .ProjectTo<GetDocumentsDocument>(_mapper.ConfigurationProvider)
            .ToPaginatedListAsync(request.Page, request.PageSize, cancellationToken);

        return result.ToPaginatedListResponse();
    }
}
