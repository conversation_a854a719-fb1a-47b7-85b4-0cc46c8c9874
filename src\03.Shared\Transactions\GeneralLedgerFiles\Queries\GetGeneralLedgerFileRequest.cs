﻿using FluentValidation;
using Microsoft.AspNetCore.Http;
using Pertamina.WebFIPosting.Shared.Common.Attributes;
using Pertamina.WebFIPosting.Shared.Common.Constants;

namespace Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgerFiles.Queries;
public class GetGeneralLedgerFileRequest
{
    [OpenApiContentType(ContentTypes.TextPlain)]
    public Guid GeneralLedgerId { get; set; }

    [OpenApiContentType(ContentTypes.MultipartFormData)]
    public IEnumerable<IFormFile> Files { get; set; } = default!;
}
public class GetGeneralLedgerFileRequestValidator : AbstractValidator<GetGeneralLedgerFileRequest>
{
    public GetGeneralLedgerFileRequestValidator()
    {
        RuleFor(x => x.GeneralLedgerId)
           .NotEmpty()
           .WithMessage("General Ledger ID is required.");
    }
}
