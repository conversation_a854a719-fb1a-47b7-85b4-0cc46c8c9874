﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgerLineDetails.Commands;

namespace Pertamina.WebFIPosting.Application.Transactions.GeneralLedgerLineDetails.Commands;
public class CreateGeneralLedgerLineDetailsCommand : GeneralLedgerLineDetailRequest, IRequest<GeneralLedgerLineDetailResponse>
{
}

public class CreateGeneralLedgerLineDetailsCommandHandler : IRequestHandler<CreateGeneralLedgerLineDetailsCommand, GeneralLedgerLineDetailResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    public CreateGeneralLedgerLineDetailsCommandHandler(IWebFiPlatformDbContext context)
    {
        _context = context;
    }
    public async Task<GeneralLedgerLineDetailResponse> Handle(CreateGeneralLedgerLineDetailsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var generalLedgerExists = await _context.GeneralLedger
                    .AnyAsync(x => x.Id == request.GeneralLedgerId, cancellationToken);
            if (!generalLedgerExists)
            {
                throw new NotFoundException($"General Ledger with ID {request.Id} not found.");
            }

            var setData = new GeneralLedgerLineDetail
            {
                GeneralLedgerId = request.GeneralLedgerId,
                Item = request.Item,
                DebitCredit = request.DebitCredit,
                CompanyCode = request.CompanyCode,
                DocumentDate = request.DocumentDate,
                PostingDate = request.PostingDate,
                Period = request.Period,
                TranslationDate = request.TranslationDate,
                GLAccount = request.GLAccount,
                Reference = request.Reference,
                DocHeaderText = request.DocHeaderText,
                DocType = request.DocType,
                Currency = request.Currency,
                AmountinDoc = request.AmountinDoc,
                AmountinLC = request.AmountinLC,
                AmountinLC2 = request.AmountinLC2,
                AmountinLC3 = request.AmountinLC3,
                Text = request.Text,
                TextCode = request.TextCode,
                Assignment = request.Assignment,
                CostCenter = request.CostCenter,
                Plant = request.Plant,
                ProfitCenter = request.ProfitCenter,
                InternalOrder = request.InternalOrder,
                Wbs = request.Wbs,
                BussinessPlace = request.BussinessPlace,
                RecIndicator = request.RecIndicator,
                ReferenceKey1 = request.ReferenceKey1,
                ReferenceKey2 = request.ReferenceKey2,
                ReferenceKey3 = request.ReferenceKey3,
                TradingPartner = request.TradingPartner,
                TransactnType = request.TransactnType,
                DocNumberPosted = request.DocNumberPosted,
                DocCurrency = request.DocCurrency,
                Year = !string.IsNullOrEmpty(request.Year) ? int.Parse(request.Year) : null,
                PostedAmmount = request.PostedAmmount,
                Message = request.Message,
                Status = request.Status,
                SimulatedResponseCode = request.SimulatedResponseCode,
                SimulatedResponseMessage = request.SimulatedResponseMessage,
                IsSimulate = request.IsSimulate,
                SimulateDate = request.SimulateDate,
            };
            await _context.GeneralLedgerLineDetail.AddAsync(setData, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            return new GeneralLedgerLineDetailResponse() { Id = setData.Id };
        }
        catch (Exception ex)
        {
            throw new NotImplementedException("Error while creating General Ledger Line Detail", ex);
        }
    }
}
