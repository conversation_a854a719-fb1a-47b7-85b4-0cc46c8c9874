﻿using MediatR;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Application.Services.Storage;
using Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgerFiles.Queries;

namespace Pertamina.WebFIPosting.Application.Transactions.GeneralLedgerFiles.Commands;
public class CreateGeneralLedgerFileCommand : GetGeneralLedgerFileRequest, IRequest<GetGeneralLedgerFileResponse>
{
}

public class CreateGeneralLedgerFileCommandHandler : IRequestHandler<CreateGeneralLedgerFileCommand, GetGeneralLedgerFileResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IStorageService _storageService;
    public CreateGeneralLedgerFileCommandHandler(IWebFiPlatformDbContext context, IStorageService storageService)
    {
        _context = context;
        _storageService = storageService;
    }

    public async Task<GetGeneralLedgerFileResponse> Handle(CreateGeneralLedgerFileCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var checkGeneralLedgerId = await _context.GeneralLedger.FindAsync(request.GeneralLedgerId);
            if (checkGeneralLedgerId == null)
            {
                throw new NotFoundException("General Ledger Not exists");
            }

            if (request.Files != null && request.Files.Any())
            {
                foreach (var file in request.Files)
                {
                    using var memoryStream = new MemoryStream();
                    await file.CopyToAsync(memoryStream, cancellationToken);
                    memoryStream.Position = 0;
                    var fileData = memoryStream.ToArray();

                    var document = new GeneralLedgerFile
                    {
                        GeneralLedgerId = request.GeneralLedgerId,
                        FileName = file.FileName,
                        FileSize = file.Length,
                        FileContentType = file.ContentType,
                        StorageFileId = await _storageService.CreateAsync(fileData)
                    };

                    await _context.GeneralLedgerFile.AddAsync(document, cancellationToken);
                }
            }

            await _context.SaveChangesAsync(cancellationToken);

            return new GetGeneralLedgerFileResponse();
        }
        catch (Exception ex)
        {
            throw new NotImplementedException("Error while creating General Ledger File", ex);
        }
    }
}
