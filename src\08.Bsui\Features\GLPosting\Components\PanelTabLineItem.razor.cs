using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components;
using System.Security.Claims;
using MudBlazor;
using Pertamina.WebFIPosting.Bsui.Common.Components;
using Pertamina.WebFIPosting.Bsui.Common.Constants;
using Pertamina.WebFIPosting.Bsui.Features.GLPosting.Constants;
using Pertamina.WebFIPosting.Shared.Common.Constants;
using Syncfusion.Blazor.Grids;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgers.Queries;
using Pertamina.WebFIPosting.Base.Enums;
using Pertamina.WebFIPosting.Shared.Common.Extensions;
using Pertamina.WebFIPosting.Shared.Common.Responses;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgers.Commands;
using Syncfusion.Blazor.Navigations;

namespace Pertamina.WebFIPosting.Bsui.Features.GLPosting.Components;
public partial class PanelTabLineItem
{
    [CascadingParameter]
    protected Task<AuthenticationState> AuthenticationStateTask { get; set; } = default!;
    protected ClaimsPrincipal _user = default!;
    private List<MudBlazor.BreadcrumbItem> _breadcrumbItems = new();
    private List<GetGeneralLedgerReponse> _getGeneralLedgerReponses = new();
    public SfGrid<GetGeneralLedgerReponse>? _gridRef;
    public GeneralLedgerRequest _request = new();
    public List<DropdownItemResponse> _company = new();
    public List<DropdownItemResponse> _currency = new();
    protected bool _isLoadingData = false;
    
    protected override async Task OnInitializedAsync()
    {
        SetupBreadcrumb();
        await SetDataOnLoadAsync();
    }

    private void SetupBreadcrumb()
    {
        _breadcrumbItems = new()
        {
            CommonBreadcrumbFor.Active(CommonDisplayTextFor.Home)
        };
    }

    private async Task SetDataOnLoadAsync()
    {
        var requestStatus = new List<string>
        {
            RequestStatus.Approved.GetDescription()
        };

        var requet = new GetGeneralLedgerRequest()
        {
            Status = string.Join(",", requestStatus),
        };
        _isLoadingData = true;
        var response = await _generalLedgerService.GetGeneralLedgerAsync(requet);
        if (response.Error is null)
        {
            _getGeneralLedgerReponses = response.Result!.Items.ToList();
        }

        _isLoadingData = false;
    }
    
    public void AddRequest(CommandClickEventArgs<LineItemTicket> args)
    {
        var selectedTicket = args;
        _navigationManager.NavigateTo(RouteFor.FormAddGLPosting(), true);
    }

    public void DetailGLPosting(GetGeneralLedgerReponse args)
    {
        var selectedTicket = args;
        _navigationManager.NavigateTo(RouteFor.FormAddGLPosting(args.Id), true);
    }
    
    private async Task ExportExcel()
    {
        var exportProperties = new ExcelExportProperties
        {
            IncludeTemplateColumn = true
        };
        await _gridRef.ExportToExcelAsync(exportProperties);
    }
    
    public async Task ToolbarClickHandler(ClickEventArgs args)
    {
        if (args.Item.Id.Contains("_excelexport")) //Id is combination of Grid's ID and itemname
        {
            var exportProperties = new ExcelExportProperties
            {
                IncludeTemplateColumn = true
            };
            await _gridRef.ExportToExcelAsync(exportProperties);
        }
    }
    
    public void ExcelQueryCellInfoHandler(ExcelQueryCellInfoEventArgs<GetGeneralLedgerReponse> args)
    {
        var a = args.Column.Field;
        if (args.Column.Field == "Status")
        {
            args.Cell.Value = args.Data.Status;
        }
    }

    //Dialog Delete
    private async Task OpenDialogAsyncDelete()
    {
        var dialog = await _dialogService.ShowAsync<DialogDelete>("Hapus Data");
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            _snackbar.Add("Data berhasil dihapus", Severity.Success);
        }
    }
}

public class LineItemTicket
{
    public string NoTicketWeb { get; set; } = default!;
    public string NoDocSAP { get; set; } = default!;
    public string GL { get; set; } = default!;
    public string Amount { get; set; } = default!;
    public string DebitCreditIndicator { get; set; } = default!;
    public string OperationalCategory3 { get; set; } = default!;
    public DateTime RequestDate { get; set; }
    public DateTime PostingDate { get; set; }
    public string Status { get; set; } = default!;
}
