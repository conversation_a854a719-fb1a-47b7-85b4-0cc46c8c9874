﻿using Pertamina.WebFIPosting.Domain.Abstracts;

namespace Pertamina.WebFIPosting.Domain.Entities.Transactions.RecordToReport;

public class GLPostingClearingLineDetail : AuditableEntity
{
    public Guid GeneralLedgerId { get; set; }
    public GeneralLedger GeneralLedger { get; set; } = default!;
    public string? Item { get; set; }
    public string? CompanyCode { get; set; }
    public DateOnly? DocumentDate { get; set; }
    public DateOnly? PostingDate { get; set; }
    public string? Period { get; set; }
    public DateOnly? TranslationDate { get; set; }
    public string? DebitCredit { get; set; }
    public string? AccountType { get; set; }
    public string? GLAccount { get; set; }
    public string? Reference { get; set; }
    public string? DocHeaderText { get; set; }
    public string? DocType { get; set; }
    public string? Currency { get; set; }
    public string? AmountinDoc { get; set; }
    public string? AmountinLC { get; set; }
    public string? AmountinLC2 { get; set; }
    public string? AmountinLC3 { get; set; }
    public string? Text { get; set; }
    public string? TaxCode { get; set; }
    public string? Assignment { get; set; }
    public string? CostCenter { get; set; }
    public string? Plant { get; set; }
    public string? ProfitCenter { get; set; }
    public string? InternalOrder { get; set; }
    public string? WBS { get; set; }
    public string? BussinessPlace { get; set; }
    public string? Rec_Indicator { get; set; }
    public string? Reference_Key_1 { get; set; }
    public string? Reference_Key_2 { get; set; }
    public string? Reference_Key_3 { get; set; }
    public string? Trading_Partner { get; set; }
    public string? TransactnType { get; set; }
    public string? NoDocumentClearing { get; set; }
    public string? YearDocumentClearing { get; set; }
    public string? AmountDocumentClearing { get; set; }
    public string? DocNumberPosted { get; set; }
    public string? DocCurrency { get; set; }
    public int? Year { get; set; }
    public string? PostedAmmount { get; set; }
    public string? Message { get; set; }
    public string? Status { get; set; }
    public string? SimulatedResponseCode { get; set; }
    public string? SimulatedResponseMessage { get; set; }
    public bool IsSimulate { get; set; }
    public DateTime? SimulateDate { get; set; }
}
