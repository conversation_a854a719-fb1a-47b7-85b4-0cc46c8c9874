﻿namespace Pertamina.WebFIPosting.Shared.Bmcs.Command;

public class UpdateWorkOrder
{
    public string? Transaction_Number { get; set; }
    public string? Activity_Type { get; set; }
    public string? Work_Order_Number { get; set; }
    public string? Proof_of_Execution { get; set; }
    public string? Reason_Cancel { get; set; }
    public string? Reason_Pending { get; set; }
    public string? Request_Manager_Company { get; set; }
    public string? Support_Group_Manager_ID { get; set; }
    public string? Manager_Support_Organization { get; set; }
    public string? Manager_Support_Group_Name { get; set; }
    public string? Assignee_Manager_Login_ID { get; set; }
    public string? Support_Company { get; set; }
    public string? Support_Group_ID { get; set; }
    public string? Support_Organization { get; set; }
    public string? Support_Group_Name { get; set; }
    public string? Assignee_Login_ID { get; set; }
    public string? Operational_Category_1 { get; set; }
    public string? Operational_Category_2 { get; set; }
    public string? Operational_Category_3 { get; set; }
}
