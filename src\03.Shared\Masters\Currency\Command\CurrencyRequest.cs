﻿using Pertamina.WebFIPosting.Shared.Common.Attributes;
using Pertamina.WebFIPosting.Shared.Common.Constants;

namespace Pertamina.WebFIPosting.Shared.Masters.Currency.Command;

public class CurrencyRequest
{
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string CurrencyCode { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? LongText { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? ShortText { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? ISOCode { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? AlternativeKey { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? ValidUntil { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string? PrimarySAPCode { get; set; } = default!;
}
