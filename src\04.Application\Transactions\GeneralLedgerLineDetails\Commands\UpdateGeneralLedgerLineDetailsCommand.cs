﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Shared.Transactions.GeneralLedgerLineDetails.Commands;

namespace Pertamina.WebFIPosting.Application.Transactions.GeneralLedgerLineDetails.Commands;

public class UpdateGeneralLedgerLineDetailsCommand : GeneralLedgerLineDetailRequest, IRequest<GeneralLedgerLineDetailResponse>
{
}

public class UpdateGeneralLedgerLineDetailsCommandValidator : AbstractValidator<UpdateGeneralLedgerLineDetailsCommand>
{
    public UpdateGeneralLedgerLineDetailsCommandValidator()
    {
        RuleFor(x => x.Id)
          .NotEmpty()
          .WithMessage("ID is Required.");
    }
}

public class UpdateGeneralLedgerLineDetailsCommandHandler : IRequestHandler<UpdateGeneralLedgerLineDetailsCommand, GeneralLedgerLineDetailResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    public UpdateGeneralLedgerLineDetailsCommandHandler(IWebFiPlatformDbContext context)
    {
        _context = context;
    }
    public async Task<GeneralLedgerLineDetailResponse> Handle(UpdateGeneralLedgerLineDetailsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var data = await _context.GeneralLedgerLineDetail
                .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);
            if (data is null)
            {
                throw new NotFoundException($"General Ledger LineDetai Id {request.Id} not found.");
            }

            var generalLedgerExists = await _context.GeneralLedger
               .AnyAsync(x => x.Id == request.GeneralLedgerId, cancellationToken);
            if (!generalLedgerExists)
            {
                throw new NotFoundException($"General Ledger with Id {request.GeneralLedgerId} not found.");
            }

            data.GeneralLedgerId = request.GeneralLedgerId;
            data.Item = request.Item;
            data.DebitCredit = request.DebitCredit;
            data.CompanyCode = request.CompanyCode;
            data.DocumentDate = request.DocumentDate;
            data.PostingDate = request.PostingDate;
            data.Period = request.Period;
            data.TranslationDate = request.TranslationDate;
            data.GLAccount = request.GLAccount;
            data.Reference = request.Reference;
            data.DocHeaderText = request.DocHeaderText;
            data.DocType = request.DocType;
            data.Currency = request.Currency;
            data.AmountinDoc = request.AmountinDoc;
            data.AmountinLC = request.AmountinLC;
            data.AmountinLC2 = request.AmountinLC2;
            data.AmountinLC3 = request.AmountinLC3;
            data.Text = request.Text;
            data.TextCode = request.TextCode;
            data.Assignment = request.Assignment;
            data.CostCenter = request.CostCenter;
            data.Plant = request.Plant;
            data.ProfitCenter = request.ProfitCenter;
            data.InternalOrder = request.InternalOrder;
            data.Wbs = request.Wbs;
            data.BussinessPlace = request.BussinessPlace;
            data.RecIndicator = request.RecIndicator;
            data.ReferenceKey1 = request.ReferenceKey1;
            data.ReferenceKey2 = request.ReferenceKey2;
            data.ReferenceKey3 = request.ReferenceKey3;
            data.TradingPartner = request.TradingPartner;
            data.TransactnType = request.TransactnType;
            data.DocNumberPosted = request.DocNumberPosted;
            data.DocCurrency = request.DocCurrency;
            data.Year = !string.IsNullOrEmpty(request.Year) ? int.Parse(request.Year) : null;
            data.PostedAmmount = request.PostedAmmount;
            data.Message = request.Message;
            data.Status = request.Status;
            data.SimulatedResponseCode = request.SimulatedResponseCode;
            data.SimulatedResponseMessage = request.SimulatedResponseMessage;
            data.IsSimulate = request.IsSimulate;
            data.SimulateDate = request.SimulateDate;

            await _context.SaveChangesAsync(cancellationToken);

            return new GeneralLedgerLineDetailResponse { Id = data.Id };
        }
        catch (Exception ex)
        {
            throw new NotImplementedException("Error while updating General Ledger Line Detail", ex);
        }
    }
}
