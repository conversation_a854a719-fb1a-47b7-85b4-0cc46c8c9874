﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel;

namespace Pertamina.WebFIPosting.Base.Enums;

public enum RequestStatus
{
    [Description("Draft")]
    [Display(Name = "Draft")]
    Draft = 0,

    [Description("Waiting For Approval")]
    [Display(Name = "Waiting For Approval")]
    WaitingForApproval = 1,

    [Description("Approved")]
    [Display(Name = "Approved")]
    Approved = 2,

    [Description("In Progress")]
    [Display(Name = "In Progress")]
    InProgress = 3,

    [Description("Assigned")]
    [Display(Name = "Assigned")]
    Assigned = 4,

    [Description("Panding")]
    [Display(Name = "Panding")]
    Panding = 5,

    [Description("Completed")]
    [Display(Name = "Completed")]
    Completed = 6,

    [Description("Rejected")]
    [Display(Name = "Rejected")]
    Rejected = 7,

    #region Status Sap
    [Description("Posted")]
    [Display(Name = "Posted")]
    Posted = 8,

    [Description("SAP Process")]
    [Display(Name = "SAP Process")]
    SAPProcess = 9,
    #endregion
}
