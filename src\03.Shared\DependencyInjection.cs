﻿/*
* InvestorRelationsHub edited by arie.sem
* Copyright (c) 2024-2025 PT. Pertamina Persero
*/

using System.Reflection;
using FluentValidation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Pertamina.WebFIPosting.Shared.Audits.Options;
using Pertamina.WebFIPosting.Shared.Documents.Options;

namespace Pertamina.WebFIPosting.Shared;

public static class DependencyInjection
{
    public static IServiceCollection AddShared(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

        #region Essential Options
        services.AddAuditOptions(configuration);
        #endregion Essential Options

        #region Business Options
        services.AddDocumentOptions(configuration);
        #endregion Business Options

        return services;
    }
}
