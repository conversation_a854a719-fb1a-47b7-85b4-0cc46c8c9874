﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Common.Mappings;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Domain.Entities.Masters;
using Pertamina.WebFIPosting.Shared.Masters.Company.Constants;
using Pertamina.WebFIPosting.Shared.Masters.Company.Queries;

namespace Pertamina.WebFIPosting.Application.Masters.Queries;
public class GetCompanyByCodeQuery : IRequest<GetCompanyByCodeQueryResponse>
{
    public string Code { get; set; } = default!;
}

public class GetCompanyByCodeQueryMapping : IMapFrom<Company, GetCompanyByCodeQueryResponse>
{
}

public class GetCompanyByCodeQueryHandler : IRequestHandler<GetCompanyByCodeQuery, GetCompanyByCodeQueryResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    private readonly IMapper _mapper;

    public GetCompanyByCodeQueryHandler(IWebFiPlatformDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<GetCompanyByCodeQueryResponse> Handle(GetCompanyByCodeQuery request, CancellationToken cancellationToken)
    {
        var query = await _context.Company
            .Where(x => x.Code == request.Code).SingleOrDefaultAsync(cancellationToken);

        if (query is null)
        {
            throw new NotFoundException(DisplayTextFor.Company, request.Code);
        }

        return _mapper.Map<GetCompanyByCodeQueryResponse>(query);
    }
}
