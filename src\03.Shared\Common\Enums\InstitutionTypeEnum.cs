﻿namespace Pertamina.WebFIPosting.Shared.Common.Enums;

public class InstitutionTypeEnum
{
    public enum InstitutionTypeCode
    {
        INVESTMENTADVISOR,
        INSURANCECOMPANY,
        TRUST,
        BANK,
        UNCLASSIFIED,
        PENSIONFUND,
        SOVEREIGWEALTHFUND,
        BROKERAGE,
    }

    public static string GetInstitutionTypeCode(string code)
    {
        if (string.IsNullOrEmpty(code))
        {
            return null;
        }

        if (Enum.TryParse<InstitutionTypeCode>(code, out var institutionTypeEnum))
        {
            return institutionTypeEnum.ToString();
        }

        return code switch
        {
            "INVESTMENTADVISOR" => InstitutionTypeCode.INVESTMENTADVISOR.ToString(),
            "INSURANCECOMPANY" => InstitutionTypeCode.INSURANCECOMPANY.ToString(),
            "TRUST" => InstitutionTypeCode.TRUST.ToString(),
            "BANK" => InstitutionTypeCode.BANK.ToString(),
            "UNCLASSIFIED" => InstitutionTypeCode.UNCLASSIFIED.ToString(),
            "PENSIONFUND" => InstitutionTypeCode.PENSIONFUND.ToString(),
            "SOVEREIGWEALTHFUND" => InstitutionTypeCode.SOVEREIGWEALTHFUND.ToString(),
            "BROKERAGE" => InstitutionTypeCode.BROKERAGE.ToString(),
            _ => null,
        };
    }

    public static string GetInstitutionTypeName(string code)
    {
        return code.ToUpper() switch
        {
            "INVESTMENTADVISOR" => "Investment Advisor",
            "INSURANCECOMPANY" => "Insurance Company",
            "TRUST" => "Trust ",
            "UNCLASSIFIED" => "Unclassified",
            "PENSIONFUND" => "Pension Fund",
            "BANK" => "Bank",
            "SOVEREIGWEALTHFUND" => "Sovereign Wealth Fund",
            "BROKERAGE" => "Brokerage",
            _ => null,
        };
    }
}
