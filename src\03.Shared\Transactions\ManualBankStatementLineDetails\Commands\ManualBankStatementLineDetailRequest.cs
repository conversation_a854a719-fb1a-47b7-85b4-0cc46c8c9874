﻿using FluentValidation;
using Pertamina.WebFIPosting.Shared.Common.Attributes;
using Pertamina.WebFIPosting.Shared.Common.Constants;

namespace Pertamina.WebFIPosting.Shared.Transactions.ManualBankStatementLineDetails.Commands;

public class ManualBankStatementLineDetailRequest
{
    [OpenApiContentType(ContentTypes.TextPlain)]
    public Guid? Id { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public Guid GeneralLedgerId { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string Item { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string DocHeaderText { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string TransactionType { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string TanggalText { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string Assignment { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string Currency { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string OpeningBalance { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string UangMasuk { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string UangKeluar { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string EndingBalance { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string DocNumberPosted { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string DocCurrency { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public int Year { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string PostedAmmount { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string Message { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string Status { get; set; } = default!;

    [OpenApiContentType(ContentTypes.TextPlain)]
    public string SimulatedResponseCode { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public string SimulatedResponseMessage { get; set; } = default!;
    [OpenApiContentType(ContentTypes.TextPlain)]
    public bool IsSimulate { get; set; }
    [OpenApiContentType(ContentTypes.TextPlain)]
    public DateTime? SimulateDate { get; set; }
}

public class ManualBankStatementLineDetailRequestValidator : AbstractValidator<ManualBankStatementLineDetailRequest>
{
    public ManualBankStatementLineDetailRequestValidator()
    {
        RuleFor(x => x.GeneralLedgerId)
            .NotEmpty()
            .WithMessage("General Ledger ID is required.");
    }
}
