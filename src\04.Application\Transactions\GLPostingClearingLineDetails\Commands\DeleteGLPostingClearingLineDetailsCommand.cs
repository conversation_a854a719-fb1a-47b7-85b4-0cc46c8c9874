﻿using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Pertamina.WebFIPosting.Application.Common.Exceptions;
using Pertamina.WebFIPosting.Application.Services.Persistence;
using Pertamina.WebFIPosting.Shared.Transactions.GLPostingClearingLineDetails.Commands;

namespace Pertamina.WebFIPosting.Application.Transactions.GLPostingClearingLineDetails.Commands;
public class DeleteGLPostingClearingLineDetailsCommand : IRequest<GLPostingClearingLineDetailResponse>
{
    public Guid Id { get; set; }
}
public class DeleteGLPostingClearingLineDetailsCommandValidator : AbstractValidator<DeleteGLPostingClearingLineDetailsCommand>
{
    public DeleteGLPostingClearingLineDetailsCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("ID is required.");
    }
}
public class DeleteGLPostingClearingLineDetailsCommandHandler : IRequestHandler<DeleteGLPostingClearingLineDetailsCommand, GLPostingClearingLineDetailResponse>
{
    private readonly IWebFiPlatformDbContext _context;
    public DeleteGLPostingClearingLineDetailsCommandHandler(IWebFiPlatformDbContext context)
    {
        _context = context;
    }

    public async Task<GLPostingClearingLineDetailResponse> Handle(DeleteGLPostingClearingLineDetailsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var existingData = await _context.GLPostingClearingLineDetail
                .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

            if (existingData == null)
            {
                throw new NotFoundException($"GLPosting Clearing Line Detail with ID {request.Id} not found.");
            }

            _context.GLPostingClearingLineDetail.Remove(existingData);
            await _context.SaveChangesAsync(cancellationToken);

            return new GLPostingClearingLineDetailResponse { Id = existingData.Id };
        }
        catch (Exception ex)
        {
            throw new NotImplementedException("Error while deleting GLPosting Clearing Line Detail", ex);
        }
    }
}
